"""
CICIDS2017数据集处理模块。
用于读取和预处理CICIDS2017数据集，提取不同类型的网络攻击数据，并执行标签编码。
"""
import os
import pandas as pd
import numpy as np
import traceback
from sklearn.preprocessing import LabelEncoder
from typing import Dict, Tuple, List, Any, Optional

def process_cicids2017_data(data_path: str, samples_per_class: int = 1000, 
                           save_csv: bool = True, output_path: Optional[str] = None,
                           random_seed: int = 42) -> pd.DataFrame:
    """处理CICIDS2017数据集。
    
    Args:
        data_path: 数据所在的路径
        samples_per_class: 每种类型要提取的样本数，默认为1000
        save_csv: 是否保存处理后的数据框到CSV文件，默认为True
        output_path: 输出CSV文件的路径，默认为flgoasyn/benchmark/RawData/cicids2017.csv
        random_seed: 随机种子，用于确保抽样结果可复现，默认为42
        
    Returns:
        处理后的DataFrame
    """
    print(f"处理CICIDS2017数据集，路径: {data_path}")
    print(f"每类样本提取数: {samples_per_class}")
    print(f"随机种子: {random_seed}")
    
    # 设置随机种子
    np.random.seed(random_seed)
    
    # 设置默认输出路径
    if output_path is None:
        # 获取项目根目录
        root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
        output_path = os.path.join(root_dir, "src/RawData/cicids2017.csv")  
    
    # 寻找csv文件
    csv_files = []
    for root, dirs, files in os.walk(data_path):
        for file in files:
            if file.endswith('.csv'):
                csv_files.append(os.path.join(root, file))
    
    if not csv_files:
        raise FileNotFoundError(f"在{data_path}路径下未找到任何csv文件")
    
    print(f"找到{len(csv_files)}个csv文件")
    
    # 分类和提取各类型数据
    ddos_data = pd.DataFrame()
    dos_data = pd.DataFrame()
    heartbleed_data = pd.DataFrame()
    bot_data = pd.DataFrame()
    bruteforce_data = pd.DataFrame()
    webattacks_data = pd.DataFrame()
    infiltration_data = pd.DataFrame()
    benign_data = pd.DataFrame()
    
    for file in csv_files:
        filename = os.path.basename(file).lower()
        try:
            # 读取数据
            df = pd.read_csv(file, low_memory=False)
            print(f"读取文件 {filename}, 形状: {df.shape}")
            
            # 清理列名（去除空格和特殊字符）
            df.columns = [col.strip() for col in df.columns]
            
            # 检查是否有包含'Label'的列（不区分大小写）
            label_col = None
            for col in df.columns:
                if 'label' in col.lower():
                    label_col = col
                    break
                    
            if label_col is None:
                print(f"警告: 文件 {filename} 中不存在包含'Label'的列，跳过此文件")
                continue
                
            # 将找到的标签列重命名为标准名称'Label'（如果不是'Label'）
            if label_col != 'Label':
                df = df.rename(columns={label_col: 'Label'})
                print(f"将列 '{label_col}' 重命名为 'Label'")
                
            # 确保Label列的值也被清理（去除空格）
            df['Label'] = df['Label'].astype(str).str.strip()
            
            # 根据文件名和类型提取数据
            if 'ddos' in filename:
                # 提取DDoS类型数据
                ddos_sample = df[df['Label'] == 'DDoS']
                if len(ddos_sample) > 0:
                    ddos_sample = ddos_sample.sample(min(samples_per_class, len(ddos_sample)), random_state=random_seed) if len(ddos_sample) > samples_per_class else ddos_sample
                    ddos_data = pd.concat([ddos_data, ddos_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(ddos_sample)} 条DDoS数据")
                
                # 提取BENIGN类型数据
                benign_sample = df[df['Label'] == 'BENIGN']
                if len(benign_sample) > 0:
                    benign_sample = benign_sample.sample(min(samples_per_class, len(benign_sample)), random_state=random_seed) if len(benign_sample) > samples_per_class else benign_sample
                    benign_data = pd.concat([benign_data, benign_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(benign_sample)} 条BENIGN数据")
            
            elif 'dos' in filename or 'heartbleed' in filename:
                # 提取DoS类型数据
                dos_sample = df[df['Label'] == 'DoS']
                if len(dos_sample) > 0:
                    dos_sample = dos_sample.sample(min(samples_per_class, len(dos_sample)), random_state=random_seed) if len(dos_sample) > samples_per_class else dos_sample
                    dos_data = pd.concat([dos_data, dos_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(dos_sample)} 条DoS数据")
                
                # 提取Heartbleed类型数据
                heartbleed_sample = df[df['Label'] == 'Heartbleed']
                if len(heartbleed_sample) > 0:
                    heartbleed_sample = heartbleed_sample.sample(min(samples_per_class, len(heartbleed_sample)), random_state=random_seed) if len(heartbleed_sample) > samples_per_class else heartbleed_sample
                    heartbleed_data = pd.concat([heartbleed_data, heartbleed_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(heartbleed_sample)} 条Heartbleed数据")
                
                # 提取BENIGN类型数据
                benign_sample = df[df['Label'] == 'BENIGN']
                if len(benign_sample) > 0:
                    benign_sample = benign_sample.sample(min(2 * samples_per_class, len(benign_sample)), random_state=random_seed) if len(benign_sample) > 2 * samples_per_class else benign_sample
                    benign_data = pd.concat([benign_data, benign_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(benign_sample)} 条BENIGN数据")
            
            elif 'bot' in filename:
                # 提取Bot类型数据
                bot_sample = df[df['Label'] == 'Bot']
                if len(bot_sample) > 0:
                    bot_sample = bot_sample.sample(min(samples_per_class, len(bot_sample)), random_state=random_seed) if len(bot_sample) > samples_per_class else bot_sample
                    bot_data = pd.concat([bot_data, bot_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(bot_sample)} 条Bot数据")
                
                # 提取BENIGN类型数据
                benign_sample = df[df['Label'] == 'BENIGN']
                if len(benign_sample) > 0:
                    benign_sample = benign_sample.sample(min(samples_per_class, len(benign_sample)), random_state=random_seed) if len(benign_sample) > samples_per_class else benign_sample
                    benign_data = pd.concat([benign_data, benign_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(benign_sample)} 条BENIGN数据")
            
            elif 'bruteforce' in filename:
                # 提取Brute Force类型数据
                bruteforce_sample = df[df['Label'] == 'Brute Force']
                if len(bruteforce_sample) == 0:
                    # 尝试其他可能的标签
                    bruteforce_sample = df[df['Label'].str.contains('Brute', case=False, na=False)]
                
                if len(bruteforce_sample) > 0:
                    bruteforce_sample = bruteforce_sample.sample(min(samples_per_class, len(bruteforce_sample)), random_state=random_seed) if len(bruteforce_sample) > samples_per_class else bruteforce_sample
                    bruteforce_data = pd.concat([bruteforce_data, bruteforce_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(bruteforce_sample)} 条Brute Force数据")
                
                # 提取BENIGN类型数据
                benign_sample = df[df['Label'] == 'BENIGN']
                if len(benign_sample) > 0:
                    benign_sample = benign_sample.sample(min(samples_per_class, len(benign_sample)), random_state=random_seed) if len(benign_sample) > samples_per_class else benign_sample
                    benign_data = pd.concat([benign_data, benign_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(benign_sample)} 条BENIGN数据")
            
            elif 'webattacks' in filename:
                # 提取Web类型数据
                web_sample = df[df['Label'] == 'Web']
                if len(web_sample) == 0:
                    # 尝试其他可能的标签
                    web_sample = df[df['Label'].str.contains('Web', case=False, na=False)]
                
                if len(web_sample) > 0:
                    web_sample = web_sample.sample(min(samples_per_class, len(web_sample)), random_state=random_seed) if len(web_sample) > samples_per_class else web_sample
                    webattacks_data = pd.concat([webattacks_data, web_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(web_sample)} 条Web Attack数据")
                
                # 提取BENIGN类型数据
                benign_sample = df[df['Label'] == 'BENIGN']
                if len(benign_sample) > 0:
                    benign_sample = benign_sample.sample(min(samples_per_class, len(benign_sample)), random_state=random_seed) if len(benign_sample) > samples_per_class else benign_sample
                    benign_data = pd.concat([benign_data, benign_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(benign_sample)} 条BENIGN数据")
            
            elif 'infilteration' in filename or 'infiltration' in filename:
                # 提取Infiltration类型数据
                infiltration_sample = df[df['Label'] == 'Infiltration']
                if len(infiltration_sample) == 0:
                    # 尝试其他可能的标签
                    infiltration_sample = df[df['Label'].str.contains('Infiltration', case=False, na=False)]
                
                if len(infiltration_sample) > 0:
                    infiltration_sample = infiltration_sample.sample(min(samples_per_class, len(infiltration_sample)), random_state=random_seed) if len(infiltration_sample) > samples_per_class else infiltration_sample
                    infiltration_data = pd.concat([infiltration_data, infiltration_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(infiltration_sample)} 条Infiltration数据")
                
                # 提取BENIGN类型数据
                benign_sample = df[df['Label'] == 'BENIGN']
                if len(benign_sample) > 0:
                    benign_sample = benign_sample.sample(min(samples_per_class, len(benign_sample)), random_state=random_seed) if len(benign_sample) > samples_per_class else benign_sample
                    benign_data = pd.concat([benign_data, benign_sample], ignore_index=True)
                    print(f"从 {filename} 提取 {len(benign_sample)} 条BENIGN数据")
            
            # 如果是其他文件，尝试提取各种类型的数据
            else:
                for label in df['Label'].unique():
                    label_sample = df[df['Label'] == label]
                    if len(label_sample) > 0:
                        # 根据标签分类
                        if label == 'BENIGN':
                            benign_sample = label_sample.sample(min(samples_per_class, len(label_sample)), random_state=random_seed) if len(label_sample) > samples_per_class else label_sample
                            benign_data = pd.concat([benign_data, benign_sample], ignore_index=True)
                            print(f"从 {filename} 提取 {len(benign_sample)} 条BENIGN数据")
                        elif 'DDoS' in label:
                            ddos_sample = label_sample.sample(min(samples_per_class, len(label_sample)), random_state=random_seed) if len(label_sample) > samples_per_class else label_sample
                            ddos_data = pd.concat([ddos_data, ddos_sample], ignore_index=True)
                            print(f"从 {filename} 提取 {len(ddos_sample)} 条DDoS数据")
                        elif 'DoS' in label:
                            dos_sample = label_sample.sample(min(samples_per_class, len(label_sample)), random_state=random_seed) if len(label_sample) > samples_per_class else label_sample
                            dos_data = pd.concat([dos_data, dos_sample], ignore_index=True)
                            print(f"从 {filename} 提取 {len(dos_sample)} 条DoS数据")
                        elif 'Heartbleed' in label:
                            heartbleed_sample = label_sample.sample(min(samples_per_class, len(label_sample)), random_state=random_seed) if len(label_sample) > samples_per_class else label_sample
                            heartbleed_data = pd.concat([heartbleed_data, heartbleed_sample], ignore_index=True)
                            print(f"从 {filename} 提取 {len(heartbleed_sample)} 条Heartbleed数据")
                        elif 'Bot' in label:
                            bot_sample = label_sample.sample(min(samples_per_class, len(label_sample)), random_state=random_seed) if len(label_sample) > samples_per_class else label_sample
                            bot_data = pd.concat([bot_data, bot_sample], ignore_index=True)
                            print(f"从 {filename} 提取 {len(bot_sample)} 条Bot数据")
                        elif 'Brute' in label or 'Force' in label:
                            bruteforce_sample = label_sample.sample(min(samples_per_class, len(label_sample)), random_state=random_seed) if len(label_sample) > samples_per_class else label_sample
                            bruteforce_data = pd.concat([bruteforce_data, bruteforce_sample], ignore_index=True)
                            print(f"从 {filename} 提取 {len(bruteforce_sample)} 条Brute Force数据")
                        elif 'Web' in label:
                            web_sample = label_sample.sample(min(samples_per_class, len(label_sample)), random_state=random_seed) if len(label_sample) > samples_per_class else label_sample
                            webattacks_data = pd.concat([webattacks_data, web_sample], ignore_index=True)
                            print(f"从 {filename} 提取 {len(web_sample)} 条Web Attack数据")
                        elif 'Infiltration' in label:
                            infiltration_sample = label_sample.sample(min(samples_per_class, len(label_sample)), random_state=random_seed) if len(label_sample) > samples_per_class else label_sample
                            infiltration_data = pd.concat([infiltration_data, infiltration_sample], ignore_index=True)
                            print(f"从 {filename} 提取 {len(infiltration_sample)} 条Infiltration数据")
        
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")
            traceback.print_exc()
    
    # 合并所有数据
    all_data = pd.concat([
        ddos_data, 
        dos_data, 
        heartbleed_data, 
        bot_data, 
        bruteforce_data, 
        webattacks_data, 
        infiltration_data, 
        benign_data
    ], ignore_index=True)
    
    if all_data.empty:
        raise ValueError("没有成功处理任何数据")
    
    print(f"合并后数据形状: {all_data.shape}")
    
    # 处理空值
    all_data = all_data.fillna(0)
    
    # 转换非数字列为数字（除了Label列）
    encoders = {}  # 存储每列的编码器，便于后续使用
    for col in all_data.columns:
        if col != 'Label':  # 不处理标签列
            if all_data[col].dtype == 'object':
                try:
                    # 尝试直接转换为数值
                    all_data[col] = pd.to_numeric(all_data[col], errors='raise')
                    print(f"将列 '{col}' 直接转换为数值类型")
                except Exception as e:
                    # 如果无法直接转换，使用LabelEncoder
                    try:
                        encoder = LabelEncoder()
                        all_data[col] = encoder.fit_transform(all_data[col].astype(str))
                        encoders[col] = encoder
                        print(f"将列 '{col}' 使用LabelEncoder映射为整数编码")
                        # 打印编码映射
                        value_mapping = {label: idx for idx, label in enumerate(encoder.classes_)}
                        print(f"  列 '{col}' 的编码映射: {value_mapping}")
                    except Exception as e2:
                        print(f"警告: 无法对列 '{col}' 进行编码: {str(e2)}")
                        # 如果编码也失败，填充为0
                        all_data[col] = 0
                        print(f"  列 '{col}' 已填充为0")
    
    # 保存编码器信息（可选）
    if encoders and save_csv:
        import pickle
        encoder_path = os.path.join(os.path.dirname(output_path), "cicids2017_encoders.pkl")
        try:
            with open(encoder_path, 'wb') as f:
                pickle.dump(encoders, f)
            print(f"特征编码器已保存到: {encoder_path}")
        except Exception as e:
            print(f"保存编码器时出错: {str(e)}")
    
    # 确保再次处理空值（转换可能产生的NaN）
    all_data = all_data.fillna(0)
    
    # 对'Label'列进行标签编码，确保BENIGN类为0
    try:
        # 先使用LabelEncoder获取所有唯一类别
        label_encoder = LabelEncoder()
        original_labels = label_encoder.fit_transform(all_data['Label'])
        
        # 创建类别映射字典
        class_mapping = {label: idx for idx, label in enumerate(label_encoder.classes_)}
        reverse_mapping = {idx: label for label, idx in class_mapping.items()}
        
        # 检查是否存在BENIGN类别（不区分大小写）
        benign_class = None
        for class_name in label_encoder.classes_:
            if class_name.upper() == 'BENIGN':
                benign_class = class_name
                break
        
        # 如果找到BENIGN类别，创建新的映射确保它为0
        if benign_class is not None:
            benign_idx = class_mapping[benign_class]
            
            # 创建新的映射关系
            new_mapping = {}
            idx_counter = 1  # 从1开始为非BENIGN类别编号
            
            # 将BENIGN类别映射为0
            new_mapping[benign_idx] = 0
            
            # 为其他类别重新分配索引
            for old_idx in range(len(label_encoder.classes_)):
                if old_idx != benign_idx:
                    new_mapping[old_idx] = idx_counter
                    idx_counter += 1
            
            # 应用新映射到标签
            all_data['Label'] = [new_mapping[idx] for idx in original_labels]
            
            # 创建更新后的类别到索引的映射用于显示
            updated_class_mapping = {reverse_mapping[old_idx]: new_idx 
                                    for old_idx, new_idx in new_mapping.items()}
            
            print("类别编码映射:")
            for class_name, idx in updated_class_mapping.items():
                print(f"  {class_name} -> {idx}")
        else:
            # 如果没有找到BENIGN类别，直接使用LabelEncoder的结果
            all_data['Label'] = original_labels
            print("警告: 未找到'BENIGN'类别，使用默认LabelEncoder编码")
            
            print("类别编码映射:")
            for class_name, idx in class_mapping.items():
                print(f"  {class_name} -> {idx}")
    except Exception as e:
        print(f"标签编码过程中出错: {str(e)}")
        traceback.print_exc()
        raise
    
    print(f"处理后的数据形状: {all_data.shape}")
    print(f"类别分布:\n{all_data['Label'].value_counts()}")
    
    # 保存处理后的数据框到CSV文件
    if save_csv and not all_data.empty:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"创建输出目录: {output_dir}")
        
        # 保存数据框到CSV文件
        all_data.to_csv(output_path, index=False)
        print(f"处理后的数据已保存到: {output_path}")
    
    return all_data

if __name__ == "__main__":
    # 如果直接运行此脚本，处理数据并保存到CSV
    import argparse
    
    parser = argparse.ArgumentParser(description='处理CICIDS2017数据集并保存为CSV')
    parser.add_argument('--data_path', type=str, default='D:/experiment/PFL/flgoasyn/benchmark/RawData/CICIDS2017', help='数据所在的路径')
    parser.add_argument('--output_path', type=str, default=None, 
                        help='输出CSV文件的路径，默认为flgoasyn/benchmark/RawData/cicids2017.csv')
    parser.add_argument('--samples_per_class', type=int, default=2000, help='每种类型要提取的样本数')
    parser.add_argument('--random_seed', type=int, default=42,
                        help='随机种子，用于确保抽样结果可复现，默认为42')
    
    args = parser.parse_args()

    # 处理数据并保存
    process_cicids2017_data(
        data_path=args.data_path,
        samples_per_class=args.samples_per_class,
        save_csv=True,
        output_path=args.output_path,
        random_seed=args.random_seed
    )
    
    print("数据处理完成！") 