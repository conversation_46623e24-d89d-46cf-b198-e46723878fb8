import numpy as np
import math
import torch
from torch import nn
import torch.nn.functional as F
import torch.nn.init as init

## Intrusion Detection Models for Federated Learning
## This module implements light CNN + Transformer fusion architecture for network intrusion detection
## Designed specifically for CSV-based security datasets (CarH, CICIDS2017, ToNIoT)
## Compatible with federated learning frameworks and supports variable sequence lengths

# Custom PyTorch layers converted from TensorFlow

class CustomMultiheadAttention(nn.Module):
    """
    自定义多头注意力机制
    """
    def __init__(self, d_model, num_heads, dropout=0.1):
        super(CustomMultiheadAttention, self).__init__()
        assert d_model % num_heads == 0

        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        self.scale = math.sqrt(self.head_dim)

        # 线性投影层
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        self.out_linear = nn.Linear(d_model, d_model)

        self.dropout = nn.Dropout(dropout)

    def forward(self, query, key, value, attn_mask=None, key_padding_mask=None):
        batch_size, seq_len, _ = query.shape

        # 线性投影
        Q = self.q_linear(query)  # (batch_size, seq_len, d_model)
        K = self.k_linear(key)
        V = self.v_linear(value)


        # 重塑为多头格式
        Q = Q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale

        # 应用掩码
        if attn_mask is not None:
            scores = scores.masked_fill(attn_mask == 0, -1e9)

        # Softmax with numerical stability
        attn_weights = F.softmax(scores, dim=-1)

        attn_weights = self.dropout(attn_weights)

        # 应用注意力权重
        attn_output = torch.matmul(attn_weights, V)

        # 重塑回原始格式
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )

        # 最终线性投影
        output = self.out_linear(attn_output)

        return output, attn_weights.mean(dim=1)  # 返回平均注意力权重





class TokenLearner(nn.Module):
    """
    Attention-based token learning layer that reduces sequence length.

    Converts from TensorFlow TokenLearner to PyTorch implementation.
    Reduces sequence length to a fixed number of learned tokens.
    """

    def __init__(self, num_tokens=4, channels=None):
        """
        Initialize TokenLearner.

        Args:
            num_tokens: Number of output tokens to learn (default: 4)
            channels: Number of input channels (if None, will be initialized dynamically)
        """
        super(TokenLearner, self).__init__()
        self.num_tokens = num_tokens
        self.channels = channels

        if channels is not None:
            # Initialize layers immediately if channels is known
            self.attention = nn.Linear(channels, num_tokens)
            self.projection = nn.Linear(channels, channels)
        else:
            # Dynamic initialization for backward compatibility
            self.attention = None
            self.projection = None


    def forward(self, x):
        """
        Forward pass through TokenLearner.

        Args:
            x: Input tensor of shape (batch_size, seq_len, channels)

        Returns:
            Learned tokens of shape (batch_size, num_tokens, channels)
        """
        batch_size, seq_len, channels = x.shape

        # Initialize layers on first forward pass if not already initialized
        if self.attention is None:
            self.attention = nn.Linear(channels, self.num_tokens).to(x.device)
            self.projection = nn.Linear(channels, channels).to(x.device)
         

        # Compute attention weights: (batch, seq_len, num_tokens)
        attn = self.attention(x)
        # Add numerical stability to softmax
        attn = F.softmax(attn, dim=1)

        # Transpose to (batch, num_tokens, seq_len) and compute tokens
        attn = attn.transpose(1, 2)  # (batch, num_tokens, seq_len)
        tokens = torch.matmul(attn, x)  # (batch, num_tokens, channels)

        # Apply projection
        return self.projection(tokens)


class SEBlock(nn.Module):
    """
    Squeeze-and-Excitation block for channel attention.

    Converts from TensorFlow SEBlock to PyTorch implementation.
    Applies channel-wise attention to enhance important features.
    """

    def __init__(self, channels, reduction=8):
        """
        Initialize SEBlock.

        Args:
            channels: Number of input channels
            reduction: Reduction ratio for bottleneck (default: 8)
        """
        super(SEBlock, self).__init__()
        self.pool = GlobalAveragePooling()
        self.fc1 = nn.Linear(channels, channels // reduction)
        self.fc2 = nn.Linear(channels // reduction, channels)

    def forward(self, x):
        """
        Forward pass through SEBlock.

        Args:
            x: Input tensor of shape (batch_size, seq_len, channels)

        Returns:
            Attention-weighted tensor of same shape as input
        """
        # x shape: (batch_size, seq_len, channels)
        # Global average pooling: (batch_size, channels)
        squeeze = self.pool(x)  # GlobalAveragePooling handles the dimension automatically

        # Excitation
        excite = F.relu(self.fc1(squeeze))
        excite = torch.sigmoid(self.fc2(excite))
        excite = excite.unsqueeze(1)  # (batch_size, 1, channels)

        # Apply attention weights
        return x * excite


class LearnablePositionalEncoding(nn.Module):
    """
    Learnable positional encoding layer.

    Converts from TensorFlow LearnablePositionalEncoding to PyTorch implementation.
    Adds trainable positional embeddings to input sequences.
    """

    def __init__(self, seq_len, d_model):
        """
        Initialize LearnablePositionalEncoding.

        Args:
            seq_len: Maximum sequence length
            d_model: Model dimension (embedding size)
        """
        super(LearnablePositionalEncoding, self).__init__()
        self.seq_len = seq_len
        self.d_model = d_model

        # Learnable positional embeddings
        self.pe = nn.Parameter(torch.randn(1, seq_len, d_model))

    def forward(self, x):
        """
        Forward pass through LearnablePositionalEncoding.

        Args:
            x: Input tensor of shape (batch_size, seq_len, d_model)

        Returns:
            Input with added positional encoding
        """
        seq_len = x.size(1)
        return x + self.pe[:, :seq_len, :]


class AttentionPooling(nn.Module):
    """
    Attention-based pooling mechanism.

    Converts from TensorFlow AttentionPooling to PyTorch implementation.
    Uses attention weights to pool sequence features into a single vector.
    """

    def __init__(self, units):
        """
        Initialize AttentionPooling.

        Args:
            units: Hidden units for attention computation
        """
        super(AttentionPooling, self).__init__()
        self.W = nn.Linear(units, units)
        self.V = nn.Linear(units, 1)

    def forward(self, inputs):
        """
        Forward pass through AttentionPooling.

        Args:
            inputs: Input tensor of shape (batch_size, seq_len, features)

        Returns:
            Pooled tensor of shape (batch_size, features)
        """
        # Compute attention scores
        scores = self.V(torch.tanh(self.W(inputs)))  # (batch_size, seq_len, 1)
        weights = F.softmax(scores, dim=1)  # (batch_size, seq_len, 1)

        # Apply attention weights and sum
        return torch.sum(inputs * weights, dim=1)  # (batch_size, features)


class GlobalAveragePooling(nn.Module):
    """
    Global Average Pooling layer.

    Equivalent to TensorFlow's GlobalAveragePooling1D.
    Computes the average of each feature across the sequence dimension.
    """

    def __init__(self):
        """Initialize GlobalAveragePooling."""
        super(GlobalAveragePooling, self).__init__()

    def forward(self, x):
        """
        Forward pass through GlobalAveragePooling.

        Args:
            x: Input tensor of shape (batch_size, seq_len, features)

        Returns:
            Pooled tensor of shape (batch_size, features)
        """
        if len(x.shape) == 3:
            # x is (batch_size, seq_len, features)
            # Average over sequence dimension (dim=1)
            return torch.mean(x, dim=1)  # (batch_size, features)
        else:
            raise ValueError(f"Expected 3D input tensor, got {len(x.shape)}D tensor")















class IntrusionDetectionModel(nn.Module):
    """
    Light CNN + Transformer fusion model for network intrusion detection.

    This model combines dual paths with bi-directional cross-attention:
    1. CNN Path: Light convolutional layers with SE attention
    2. Transformer Path: Dense projection with learnable positional encoding
    3. TokenLearner: Reduces sequence length to 4 tokens in both paths
    4. Bi-directional Cross-Attention: CNN and Transformer features interact
    5. Global pooling and classification

    Architecture Overview:
    Input (N, T, f) -> [CNN Path] -> TokenLearner -> (N, 4, 32)
                    -> [Transformer Path] -> TokenLearner -> (N, 4, 32)
    -> Bi-directional Cross-Attention -> Global Pooling -> Classification

    Supports variable sequence lengths and multiple intrusion detection datasets.
    """
    def __init__(self, input_dim, num_classes, seq_len=1, d_model=128, nhead=1,
                 dropout=0.3, hidden_dim=64, cnn_channels=32):
        """
        Initialize the light CNN + Transformer fusion model.

        Args:
            input_dim: Number of input features (11 for CarH, 78 for CICIDS2017, 42 for ToNIoT)
            num_classes: Number of output classes (5 for CarH, 8 for CICIDS2017, 10 for ToNIoT)
            seq_len: Sequence length (T dimension, typically 1-50)
            d_model: Model dimension for both CNN and Transformer paths (default: 128)
            nhead: Number of attention heads for cross-attention (default: 1)
            dropout: Dropout rate for regularization (default: 0.3)
            hidden_dim: Hidden dimension for final classifier (default: 64)
            cnn_channels: Number of CNN channels for CNN path (default: 32)
        """
        super(IntrusionDetectionModel, self).__init__()

        # Store model configuration
        self.input_dim = input_dim
        self.num_classes = num_classes
        self.seq_len = seq_len
        self.d_model = d_model
        self.nhead = nhead
        self.dropout = dropout
        self.hidden_dim = hidden_dim
        self.cnn_channels = cnn_channels

        # --- CNN Path ---
        self.cnn_conv = nn.Conv1d(input_dim, cnn_channels, kernel_size=3, padding=1)
        self.cnn_bn = nn.BatchNorm1d(cnn_channels)
        self.cnn_se = SEBlock(cnn_channels, reduction=4)
        self.cnn_token_learner = TokenLearner(num_tokens=4, channels=cnn_channels)

        # --- Transformer Path ---
        self.trans_projection = nn.Linear(input_dim, d_model)
        self.trans_pos_encoding = LearnablePositionalEncoding(seq_len, d_model)
        self.trans_layer_norm = nn.LayerNorm(d_model)
        self.trans_token_learner = TokenLearner(num_tokens=4, channels=d_model)

        # --- Bi-directional Cross-Attention ---
        # Both attention modules use the same dimension for compatibility
        self.cross_attn1 = CustomMultiheadAttention(d_model=cnn_channels, num_heads=nhead, dropout=dropout)
        self.cross_attn2 = CustomMultiheadAttention(d_model=cnn_channels, num_heads=nhead, dropout=dropout)

        # Projection layers to align dimensions
        self.cnn_proj_to_channels = nn.Linear(cnn_channels, cnn_channels)  # Identity projection for CNN features
        self.trans_proj_to_channels = nn.Linear(d_model, cnn_channels)  # Project transformer features to cnn_channels

        # --- Final Classification ---
        self.global_pool = GlobalAveragePooling()
        self.classifier = nn.Sequential(
            nn.Linear(cnn_channels * 2, hidden_dim),  # cnn_channels*2 from concatenated features
            # nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_classes)
        )


    def forward(self, x):
        """
        Forward pass through the light CNN + Transformer fusion model.

        Processing Pipeline:
        1. Input shape handling
        2. Dual path processing (CNN + Transformer)
        3. TokenLearner for sequence reduction
        4. Bi-directional cross-attention
        5. Global pooling and classification

        Args:
            x: Input tensor with flexible shapes:
               - (batch_size, input_dim): Single time step
               - (batch_size, seq_len, input_dim): Sequence data
        Returns:
            Output tensor of shape (batch_size, num_classes)
            Contains logits for each class
        """
        # Handle different input shapes for flexibility
        if len(x.shape) == 2:
            # Single time step: (batch_size, input_dim) -> (batch_size, 1, input_dim)
            x = x.unsqueeze(1)
        elif len(x.shape) > 3:
            # Flatten extra dimensions to ensure (batch_size, T, f) format
            x = x.view(x.size(0), -1, x.size(-1))

        # --- CNN Path ---
        # x shape: (batch_size, seq_len, input_dim)
        # Transpose for Conv1d: (batch_size, input_dim, seq_len)
        cnn_input = x.transpose(1, 2)
        cnn1 = F.relu(self.cnn_conv(cnn_input))  # (batch_size, 32, seq_len)
        # print('==========================================', cnn1 , cnn1.size())      
        cnn2 = self.cnn_bn(cnn1)
        # Transpose back for SE and TokenLearner: (batch_size, seq_len, 32)
        cnn3 = cnn2.transpose(1, 2)
        cnn4 = self.cnn_se(cnn3)  # (batch_size, seq_len, 32)
        cnn_feat = self.cnn_token_learner(cnn4)  # (batch_size, 4, 32)

       

        # --- Transformer Path ---
        trans = self.trans_projection(x)  # (batch_size, seq_len, d_model) 
        trans1 = self.trans_pos_encoding(trans)  # (batch_size, seq_len, d_model)
        trans2 = self.trans_layer_norm(trans1)  # (batch_size, seq_len, d_model)
        trans_feat = self.trans_token_learner(trans2)  # (batch_size, 4, d_model)

        # --- Bi-directional Cross-Attention ---
        # Project features to common dimension (cnn_channels) for cross-attention
        cnn_feat_proj = self.cnn_proj_to_channels(cnn_feat)  # (batch_size, 4, cnn_channels)
        trans_feat_proj = self.trans_proj_to_channels(trans_feat)  # (batch_size, 4, cnn_channels)

        # CNN features attend to Transformer features
        attn1, _ = self.cross_attn1(cnn_feat_proj, trans_feat_proj, trans_feat_proj)  # (batch_size, 4, cnn_channels)
        # Transformer features attend to CNN features
        attn2, _ = self.cross_attn2(trans_feat_proj, cnn_feat_proj, cnn_feat_proj)  # (batch_size, 4, cnn_channels)

        # Concatenate attention outputs (both are now cnn_channels-dimensional)
        merged = torch.cat([attn1, attn2], dim=-1)  # (batch_size, 4, cnn_channels*2)

        # --- Global Pooling + Classification ---
        # Global average pooling: (batch_size, 64)
        pooled = self.global_pool(merged)  # GlobalAveragePooling handles dimensions automatically

        # Final classification
        output = self.classifier(pooled)  # (batch_size, num_classes)

        return output


class CNNTransformerModel(nn.Module):
    """
    CNN + Transformer parallel fusion model for network intrusion detection.

    This model combines two parallel feature extraction paths:
    1. CNN Path: Multiple Conv1d layers for local pattern extraction
    2. Transformer Path: Self-attention layers for global dependency modeling
    3. Feature Fusion: Concatenate CNN and Transformer outputs
    4. Final Classification: Global pooling + classifier
    """

    def __init__(self, input_dim, num_classes, seq_len=1, cnn_layers=2, transformer_layers=2,
                 d_model=128, nhead=4, cnn_channels=64, dropout=0.1, hidden_dim=256, projection_dim=None):
        """
        Initialize CNN + Transformer parallel fusion model.

        Args:
            input_dim: Number of input features (e.g., 78 for CICIDS2017)
            num_classes: Number of output classes (e.g., 8 for CICIDS2017)
            seq_len: Sequence length for temporal modeling (default: 1)
            cnn_layers: Number of CNN layers (default: 2)
            transformer_layers: Number of transformer encoder layers (default: 2)
            d_model: Model dimension for transformer (default: 128)
            nhead: Number of attention heads (default: 4)
            cnn_channels: Number of CNN channels (default: 64)
            dropout: Dropout rate for regularization (default: 0.1)
            hidden_dim: Hidden dimension for final classifier (default: 256)
            projection_dim: Input projection dimension (default: None, uses d_model)
        """
        super(CNNTransformerModel, self).__init__()

        self.input_dim = input_dim
        self.num_classes = num_classes
        self.seq_len = seq_len
        self.cnn_layers = cnn_layers
        self.transformer_layers = transformer_layers
        self.d_model = d_model
        self.nhead = nhead
        self.cnn_channels = cnn_channels
        self.dropout = dropout
        self.hidden_dim = hidden_dim

        # Set projection dimension (use projection_dim if provided, otherwise use d_model)
        self.projection_dim = projection_dim if projection_dim is not None else d_model

        # --- CNN Path ---
        self.cnn_path = nn.ModuleList()
        for i in range(cnn_layers):
            in_channels = input_dim if i == 0 else cnn_channels
            out_channels = cnn_channels

            cnn_block = nn.Sequential(
                nn.Conv1d(in_channels, out_channels, kernel_size=5, padding=2),
                nn.BatchNorm1d(out_channels), # GroupNorm(num_groups=min(1, out_channels), num_channels=out_channels), #
                nn.ReLU(),
                # nn.MaxPool1d(kernel_size=2, stride=1, padding=1) if seq_len > 1 else nn.Identity(),
            )
            self.cnn_path.append(cnn_block)

        # --- Transformer Path ---
        # Input projection to projection dimension
        self.trans_projection = nn.Linear(input_dim, self.projection_dim)

        # Additional projection if projection_dim != d_model
        if self.projection_dim != d_model:
            self.projection_to_d_model = nn.Linear(self.projection_dim, d_model)
        else:
            self.projection_to_d_model = None

        self.transformer_layers_list = nn.ModuleList()

        for i in range(transformer_layers):
            # 自注意力层
            self_attn = CustomMultiheadAttention(d_model, nhead, dropout)

            # 前馈网络
            linear1 = nn.Linear(d_model, d_model*2)
            linear2 = nn.Linear(d_model*2, d_model)

            # 层归一化
            norm1 = nn.LayerNorm(d_model, eps=1e-6)
            norm2 = nn.LayerNorm(d_model, eps=1e-6)

            # Dropout
            dropout1 = nn.Dropout(dropout)
            dropout2 = nn.Dropout(dropout)
            dropout_ff = nn.Dropout(dropout)

            layer_dict = nn.ModuleDict({
                'self_attn': self_attn,
                'linear1': linear1,
                'linear2': linear2,
                'norm1': norm1,
                'norm2': norm2,
                'dropout1': dropout1,
                'dropout2': dropout2,
                'dropout_ff': dropout_ff
            })

            self.transformer_layers_list.append(layer_dict)

        # --- Global Pooling ---
        self.global_pool = GlobalAveragePooling()

        # --- Final Classifier ---
        # CNN输出: cnn_channels, Transformer输出: d_model
        fusion_dim = cnn_channels + d_model
        self.classifier = nn.Sequential(
            nn.Linear(fusion_dim, hidden_dim),
            # nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_classes)
        )


    def forward(self, x, return_features=False):
        """
        Forward pass through CNN + Transformer parallel fusion model.

        Args:
            x: Input tensor with flexible shapes:
               - (batch_size, input_dim): Single time step
               - (batch_size, seq_len, input_dim): Sequence data
            return_features: If True, returns both output and features before classifier

        Returns:
            If return_features=False: Output tensor of shape (batch_size, num_classes)
            If return_features=True: Tuple of (output, features_before_classifier)
        """
        # Handle different input shapes
        if len(x.shape) == 2:
            # Single time step: (batch_size, input_dim) -> (batch_size, 1, input_dim)
            x = x.unsqueeze(1)
        elif len(x.shape) > 3:
            # Flatten extra dimensions to ensure (batch_size, T, f) format
            x = x.view(x.size(0), -1, x.size(-1))

        batch_size = x.size(0)

        # --- CNN Path ---
        # Transpose for Conv1d: (batch_size, input_dim, seq_len)
        cnn_input = x.transpose(1, 2)  # (batch_size, input_dim, seq_len)
        cnn_out = cnn_input

        for cnn_block in self.cnn_path:
            cnn_out = cnn_block(cnn_out)  # (batch_size, cnn_channels, seq_len)

        # Transpose back: (batch_size, seq_len, cnn_channels)
        cnn_features = cnn_out.transpose(1, 2)

        # --- Transformer Path ---
        # Project input features to projection dimension
        trans_projected = self.trans_projection(x)  # (batch_size, seq_len, projection_dim)

        # Additional projection to d_model if needed
        if self.projection_to_d_model is not None:
            trans_input = self.projection_to_d_model(trans_projected)  # (batch_size, seq_len, d_model)
        else:
            trans_input = trans_projected  # (batch_size, seq_len, d_model)

        trans_out = trans_input

        # Apply transformer layers
        for layer_dict in self.transformer_layers_list:
            # Self-attention block
            src2, _ = layer_dict['self_attn'](
                trans_out, trans_out, trans_out
            )
            src2 = layer_dict['dropout1'](src2)
            trans_out = trans_out + src2  # 残差连接
            trans_out = layer_dict['norm1'](trans_out)

            # Feed forward block
            src2 = layer_dict['linear1'](trans_out)
            src2 = F.relu(src2)
            src2 = layer_dict['dropout_ff'](src2)
            src2 = layer_dict['linear2'](src2)

            src2 = layer_dict['dropout2'](src2)
            trans_out = trans_out + src2  # 残差连接
            trans_out = layer_dict['norm2'](trans_out)

        # --- Feature Fusion ---
        # Global average pooling for both paths
        cnn_pooled = self.global_pool(cnn_features)      # (batch_size, cnn_channels)
        trans_pooled = self.global_pool(trans_out)       # (batch_size, d_model)

        # Concatenate features
        fused_features = torch.cat([cnn_pooled, trans_pooled], dim=1)  # (batch_size, cnn_channels + d_model)

        # --- Final Classification ---
        output = self.classifier(fused_features)  # (batch_size, num_classes)

        if return_features:
            return output, fused_features
        else:
            return output


# Convenience functions for creating models for different datasets

def create_intrusion_detection_model(dataset_name, input_dim, seq_len=1, d_model=32,
                                    nhead=1, dropout=0.3, hidden_dim=64, cnn_channels=32):
    """
    Factory function to create light CNN + Transformer fusion models for specific datasets.

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        seq_len: Sequence length for temporal modeling
        d_model: Model dimension for both CNN and Transformer paths
        nhead: Number of attention heads for cross-attention
        dropout: Dropout rate for regularization
        hidden_dim: Hidden dimension for final classifier
        cnn_channels: Number of CNN channels for CNN path

    Returns:
        IntrusionDetectionModel instance configured for the dataset
    """
    # Dataset-specific configurations
    dataset_configs = {
        'carh': {'classes': 5},
        'cicids2017': {'classes': 8},
        'toniot': {'classes': 10}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    config = dataset_configs[dataset_name]
    num_classes = config['classes']

    # Create and return the configured model
    model = IntrusionDetectionModel(
        input_dim=input_dim,
        num_classes=num_classes,
        seq_len=seq_len,
        d_model=d_model,
        nhead=nhead,
        dropout=dropout,
        hidden_dim=hidden_dim,
        cnn_channels=cnn_channels
    )

    return model




class TransformerOnlyModel(nn.Module):
    """
    Pure Transformer model for network intrusion detection.

    This model uses only Transformer architecture without convolution layers,
    focusing purely on self-attention mechanisms to capture global patterns
    and long-range dependencies in network traffic data.

    Architecture:
    Input (N, T, f) -> Input Projection -> Transformer Encoder -> Global Pooling -> Classification
    """

    def __init__(self, input_dim, num_classes, seq_len=1, transformer_layers=4,
                 d_model=256, nhead=8, dropout=0.1, hidden_dim=512, projection_dim=64):
        """
        Initialize pure Transformer model for intrusion detection.

        Args:
            input_dim: Number of input features (e.g., 78 for CICIDS2017)
            num_classes: Number of output classes (e.g., 8 for CICIDS2017)
            seq_len: Sequence length for temporal modeling (default: 1)
            transformer_layers: Number of transformer encoder layers (default: 4)
            d_model: Model dimension for transformer (default: 256)
            nhead: Number of attention heads (default: 8)
            dropout: Dropout rate for regularization (default: 0.1)
            hidden_dim: Hidden dimension for final classifier (default: 512)
            projection_dim: Input projection dimension (default: None, uses d_model)
        """
        super(TransformerOnlyModel, self).__init__()

        # Store model configuration
        self.input_dim = input_dim
        self.num_classes = num_classes
        self.seq_len = seq_len
        self.transformer_layers = transformer_layers
        self.d_model = d_model
        self.nhead = nhead
        self.dropout = dropout
        self.hidden_dim = hidden_dim

        # Set projection dimension (use projection_dim if provided, otherwise use d_model)
        self.projection_dim = projection_dim if projection_dim is not None else d_model

        # Input projection to projection dimension
        self.input_projection = nn.Linear(input_dim, self.projection_dim)

        # Additional projection if projection_dim != d_model
        if self.projection_dim != d_model:
            self.projection_to_d_model = nn.Linear(self.projection_dim, d_model)
        else:
            self.projection_to_d_model = None

        # Create custom transformer encoder layers directly
        self.transformer_layers = nn.ModuleList()
        for i in range(transformer_layers):
            # 自注意力层
            self_attn = CustomMultiheadAttention(d_model, nhead, dropout)

            # 前馈网络
            linear1 = nn.Linear(d_model, d_model)
            linear2 = nn.Linear(d_model, d_model)

            # 层归一化 LearnablePositionalEncoding
            norm1 = nn.LayerNorm(d_model, eps=1e-6)
            norm2 = nn.LayerNorm(d_model, eps=1e-6)

            # Dropout
            dropout1 = nn.Dropout(dropout)
            dropout2 = nn.Dropout(dropout)
            dropout_ff = nn.Dropout(dropout)


            # 将层组件存储为字典
            layer_dict = nn.ModuleDict({
                'self_attn': self_attn,
                'linear1': linear1,
                'linear2': linear2,
                'norm1': norm1,
                'norm2': norm2,
                'dropout1': dropout1,
                'dropout2': dropout2,
                'dropout_ff': dropout_ff
            })

            self.transformer_layers.append(layer_dict)

        # Global pooling to convert sequences to fixed-size representations
        self.global_avg_pool = GlobalAveragePooling()

        # Final classifier
        self.classifier = nn.Sequential(
            nn.Linear(d_model, hidden_dim),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_classes)
        )

    def forward(self, x):
        """
        Forward pass through pure Transformer model.

        Args:
            x: Input tensor with flexible shapes:
               - (batch_size, input_dim): Single time step
               - (batch_size, seq_len, input_dim): Sequence data
        Returns:
            Output tensor of shape (batch_size, num_classes)
            Contains logits for each class
        """
        # Handle different input shapes
        if len(x.shape) == 2:
            # Single time step: (batch_size, input_dim) -> (batch_size, 1, input_dim)
            x = x.unsqueeze(1)
        elif len(x.shape) > 3:
            # Flatten extra dimensions to ensure (batch_size, T, f) format
            x = x.view(x.size(0), -1, x.size(-1))


        # Project input features to projection dimension
        x_projected = self.input_projection(x)  # (batch_size, seq_len, projection_dim)

        # Additional projection to d_model if needed
        if self.projection_to_d_model is not None:
            x_projected = self.projection_to_d_model(x_projected)  # (batch_size, seq_len, d_model)

        # print('=======================x_projected', x_projected, x_projected.size())

        # Transformer processing - apply custom transformer encoder layers
        transformer_out = x_projected
        for i, layer_dict in enumerate(self.transformer_layers):
            # print(f"🔍 处理第 {i} 层...")

            # Self-attention block
            src2, _ = layer_dict['self_attn'](
                transformer_out, transformer_out, transformer_out
            )
            src2 = layer_dict['dropout1'](src2)
            transformer_out = transformer_out + src2  # 残差连接
            transformer_out = layer_dict['norm1'](transformer_out)

            # Feed forward block
            src2 = layer_dict['linear1'](transformer_out)
            src2 = F.relu(src2)
            src2 = layer_dict['dropout_ff'](src2)
            src2 = layer_dict['linear2'](src2)

            src2 = layer_dict['dropout2'](src2)
            transformer_out = transformer_out + src2  # 残差连接
            transformer_out = layer_dict['norm2'](transformer_out)

        # transformer_out shape: (batch_size, seq_len, d_model)
        # print('=======================transformer_out',  transformer_out.size(), transformer_out)
        
        
        # print('=======================transformer_out', transformer_out)
        # Global pooling: Convert sequences to fixed-size vectors
        pooled_out = self.global_avg_pool(transformer_out)  # (batch_size, d_model)
        # print('=======================pooled_out', pooled_out)

        # Classification
        output = self.classifier(pooled_out)  # (batch_size, num_classes)

        return output


def create_cnn_transformer_model(dataset_name, input_dim, seq_len=1, cnn_layers=2, transformer_layers=2,
                                d_model=128, nhead=4, cnn_channels=64, dropout=0.1, hidden_dim=256, projection_dim=None):
    """
    Factory function to create CNN + Transformer parallel fusion models for specific datasets.

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        seq_len: Sequence length for temporal modeling
        cnn_layers: Number of CNN layers
        transformer_layers: Number of transformer encoder layers
        d_model: Model dimension for transformer
        nhead: Number of attention heads
        cnn_channels: Number of CNN channels
        dropout: Dropout rate for regularization
        hidden_dim: Hidden dimension for final classifier
        projection_dim: Input projection dimension (default: None, uses d_model)

    Returns:
        CNNTransformerModel instance configured for the dataset
    """
    # Dataset-specific configurations
    if dataset_name.lower() == 'carh':
        num_classes = 5  # CarH has 5 classes
    elif dataset_name.lower() == 'cicids2017':
        num_classes = 8  # CICIDS2017 has 8 classes
    elif dataset_name.lower() == 'toniot':
        num_classes = 10  # ToNIoT has 10 classes
    else:
        # Default configuration
        num_classes = 8

    # Create and return the configured model
    model = CNNTransformerModel(
        input_dim=input_dim,
        num_classes=num_classes,
        seq_len=seq_len,
        cnn_layers=cnn_layers,
        transformer_layers=transformer_layers,
        d_model=d_model,
        nhead=nhead,
        cnn_channels=cnn_channels,
        dropout=dropout,
        hidden_dim=hidden_dim,
        projection_dim=projection_dim
    )

    return model


def create_transformer_only_model(dataset_name, input_dim, seq_len=1, transformer_layers=4,
                                 d_model=256, nhead=8, dropout=0.1, hidden_dim=512, projection_dim=None):
    """
    Factory function to create pure Transformer models for specific datasets.

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        seq_len: Sequence length for temporal modeling
        transformer_layers: Number of transformer encoder layers
        d_model: Model dimension for transformer
        nhead: Number of attention heads
        dropout: Dropout rate for regularization
        hidden_dim: Hidden dimension for final classifier
        projection_dim: Input projection dimension (default: None, uses d_model)

    Returns:
        TransformerOnlyModel instance configured for the dataset
    """
    # Dataset-specific configurations
    dataset_configs = {
        'carh': {'classes': 5},
        'cicids2017': {'classes': 8},
        'toniot': {'classes': 10}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    config = dataset_configs[dataset_name]
    num_classes = config['classes']

    model = TransformerOnlyModel(
        input_dim=input_dim,
        num_classes=num_classes,
        seq_len=seq_len,
        transformer_layers=2,  # Reduce layers for stability
        d_model=64,  # Smaller model dimension
        nhead=4,  # Fewer attention heads
        dropout=0.2,  # Higher dropout for regularization
        hidden_dim=128,  # Smaller hidden dimension
        projection_dim=projection_dim
    )

    return model


class CNNOnlyModel(nn.Module):
    """
    Pure CNN model for network intrusion detection.

    This model uses only CNN architecture without transformer layers,
    focusing purely on convolutional operations to capture local patterns
    and temporal features in network traffic data.

    Architecture:
    Input (N, f) -> Conv1D → ReLU → MaxPooling1D -> Classification
    """

    def __init__(self, input_dim, num_classes):
        """
        Initialize pure CNN model for intrusion detection (EdgeCNN style).

        Args:
            input_dim: Number of input features (e.g., 78 for CICIDS2017)
            num_classes: Number of output classes (e.g., 8 for CICIDS2017)
        """
        super(CNNOnlyModel, self).__init__()

        # Store model configuration
        self.input_dim = input_dim
        self.num_classes = num_classes

        # CNN layers using torch.nn.Sequential (following EdgeCNN style)
        # Using Conv1d and MaxPool1d with fixed channels [32, 64]
        self.conv1 = torch.nn.Sequential(torch.nn.Conv1d(1, 32, kernel_size=5, stride=1, padding=2),
                                         torch.nn.ReLU(),
                                         torch.nn.MaxPool1d(kernel_size=2),
                                         torch.nn.Conv1d(32, 64, kernel_size=5, stride=1, padding=2),
                                         torch.nn.ReLU(),
                                         torch.nn.MaxPool1d(kernel_size=2)
                                         )

        # Calculate output dimension after two MaxPool1d operations
        # Use actual MaxPool1d formula: floor((input_size - kernel_size) / stride) + 1
        # For kernel_size=2, stride=2 (default)
        dim_after_pool1 = ((input_dim - 2) // 2) + 1  # First MaxPool1d
        dim_after_pool2 = ((dim_after_pool1 - 2) // 2) + 1  # Second MaxPool1d

        # Store for dynamic calculation in forward
        self.expected_features = dim_after_pool2 * 64

        # Dense layers using torch.nn.Sequential (following EdgeCNN style)
        self.dense = torch.nn.Sequential(
            torch.nn.Linear(self.expected_features, 128),
            # torch.nn.Dropout(0.2),
            torch.nn.Linear(128, num_classes)
        )

        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        """
        Forward pass through pure CNN model (EdgeCNN style).

        Args:
            x: Input tensor of shape (batch_size, input_dim)
        Returns:
            Output tensor of shape (batch_size, num_classes)
            Contains logits for each class
        """
        # Handle input shape: (batch_size, input_dim) -> (batch_size, 1, input_dim)
        # Following EdgeCNN pattern but using actual data dimensions
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # Add channel dimension: (batch_size, 1, input_dim)

        # CNN processing following EdgeCNN style
        x4 = self.conv1(x)  # Conv1d processing: (batch_size, 64, actual_pooled_dim)

        # ✅ FIXED: Static flattening using pre-calculated dimensions
        batch_size = x4.size(0)
        x5 = x4.view(batch_size, self.expected_features)  # Use pre-calculated size

        # ✅ FIXED: Dense layers are now statically initialized
        output = self.dense(x5)

        return output  # Return logits (no softmax for training)


def create_cnn_only_model(dataset_name, input_dim, seq_len=1, cnn_layers=2,
                         cnn_channels=None, hidden_dim=256):
    """
    Factory function to create pure CNN models for specific datasets (EdgeCNN style).

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        seq_len: Sequence length for temporal modeling (ignored in EdgeCNN style)
        cnn_layers: Number of CNN layers (ignored in EdgeCNN style)
        cnn_channels: List of channel sizes for CNN layers (ignored, fixed to [32, 64])
        hidden_dim: Hidden dimension for final classifier (ignored, fixed to 128)

    Returns:
        CNNOnlyModel instance configured for the dataset
    """
    # Dataset-specific configurations
    dataset_configs = {
        'carh': {'classes': 5},
        'cicids2017': {'classes': 8},
        'toniot': {'classes': 10}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    config = dataset_configs[dataset_name]
    num_classes = config['classes']

    # Create and return the configured model (EdgeCNN style with fixed parameters)
    model = CNNOnlyModel(
        input_dim=input_dim,
        num_classes=num_classes
    )

    return model

def weight_init(m):  #当进行压缩的时候要有初始化
    """
    Initialize weights for different layer types with safe initialization.

    Args:
        m: PyTorch module to initialize
    """
    # if isinstance(m, nn.GroupNorm):
    #     nn.init.constant_(m.weight, 1.0)
    #     nn.init.constant_(m.bias, 0.0)
    

    return
    # if isinstance(m, nn.Linear):
    #     init.xavier_normal_(m.weight.data)
    #     init.normal_(m.bias.data)
    # elif isinstance(m, nn.Conv1d):
    #     init.normal_(m.weight.data)
    #     if m.bias is not None:
    #         init.normal_(m.bias.data)
    # elif isinstance(m, nn.BatchNorm1d):
    #     init.normal_(m.weight.data, 1.0, 0.02)
    #     init.constant_(m.bias.data, 0.0)
    # elif isinstance(m, nn.LayerNorm):
    #     init.constant_(m.bias.data, 0.0)
    #     init.constant_(m.weight.data, 1.0)
    # elif isinstance(m, nn.Embedding):
    #     init.normal_(m.weight.data, 1.0, 0.02)
    # elif isinstance(m, nn.LSTM):
    #     for name, param in m.named_parameters():
    #         if 'weight' in name:
    #             init.xavier_normal_(param.data)
    #         elif 'bias' in name:
    #             init.normal_(param.data)
    # elif isinstance(m, nn.GRU):
    #     for name, param in m.named_parameters():
    #         if 'weight' in name:
    #             init.xavier_normal_(param.data)
    #         elif 'bias' in name:
    #             init.normal_(param.data)
    # return
   



# Legacy models for backward compatibility (if needed)
class LeNet5Mnist(nn.Module):
    """Legacy LeNet5 for MNIST - kept for compatibility."""
    def __init__(self):
        super(LeNet5Mnist, self).__init__()
        self.conv1 = nn.Conv2d(1, 10, kernel_size=5)
        self.conv2 = nn.Conv2d(10, 20, kernel_size=5)
        self.fc1 = nn.Linear(320, 50)
        self.fc2 = nn.Linear(50, 10)

    def forward(self, x):
        x = F.relu(F.max_pool2d(self.conv1(x), 2))
        x = F.relu(F.max_pool2d(self.conv2(x), 2))
        x = x.view(-1, x.shape[1]*x.shape[2]*x.shape[3])
        x = F.relu(self.fc1(x))
        x = self.fc2(x)
        return x

class LeNet5Cifar10(nn.Module):
    """Legacy LeNet5 for CIFAR10 - kept for compatibility."""
    def __init__(self):
        super(LeNet5Cifar10, self).__init__()
        self.conv1 = nn.Conv2d(3, 6, 5)
        self.pool = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(6, 16, 5)
        self.fc1 = nn.Linear(16 * 5 * 5, 120)
        self.fc2 = nn.Linear(120, 84)
        self.fc3 = nn.Linear(84, 10)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 16 * 5 * 5)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x

class LeNet5Cifar100(nn.Module):
    """Legacy LeNet5 for CIFAR100 - kept for compatibility."""
    def __init__(self):
        super(LeNet5Cifar100, self).__init__()
        self.conv1 = nn.Conv2d(3, 6, 5)
        self.pool = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(6, 16, 5)
        self.fc1 = nn.Linear(16 * 5 * 5, 120)
        self.fc2 = nn.Linear(120, 84)
        self.fc3 = nn.Linear(84, 100)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 16 * 5 * 5)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x


#Edge Intelligence: Federated Learning-Based Privacy Protection Framework for Smart Healthcare Systems, 2022
###############################CNN##############################
class EdgeCNN(nn.Module):
    def __init__(self, n_classes):
        super(EdgeCNN, self).__init__()
        self.conv1 = torch.nn.Sequential(torch.nn.Conv2d(1, 16, kernel_size=5, stride=1, padding=2),
                                         torch.nn.ReLU(),
                                         torch.nn.MaxPool2d(kernel_size=2),
                                         torch.nn.Conv2d(16, 32, kernel_size=5, stride=1, padding=2),
                                         torch.nn.ReLU(),
                                         torch.nn.MaxPool2d(kernel_size=2)
                                         )

        self.dense = torch.nn.Sequential(torch.nn.Linear(7 * 7 * 32, 128),
                                         torch.nn.Linear(128, n_classes))

        self.softmax = nn.Softmax(dim=1)


    def forward(self, x):
        rest = 28 * 28
        temp = torch.zeros((x.size(0), x.size(1), (rest - x.size(2)))).to(torch.device('cuda'))
        # x1 = x.view(x.size(0), x.size(1), x.size(2), 1)
        x2 = torch.cat([x, temp], dim=-1)
        x3 = x2.view(x.size(0), x.size(1), 28, 28)

        x4 = self.conv1(x3)
        x5 = x4.view(x.size(0), 7 * 7 * 32)  # 将输出的特征图展开成一维向量
        output = self.dense(x5)
        return output #self.softmax(output)



#DFF-SC4N: A Deep Federated Defence Framework for Protecting Supply Chain 4.0 Networks, TII, 2023
class GRU(nn.Module):
    def __init__(self, dim_in, dim_out, lstm_num_layers=1, dim_hidden=64):
        super(GRU, self).__init__()

        self.hidden_dim = dim_hidden
        self.num_layers = lstm_num_layers

        # gru
        self.bigru = nn.GRU(input_size=dim_in, hidden_size=dim_hidden, num_layers=self.num_layers, bidirectional=False, batch_first=True)
        self.linear = nn.Linear(dim_hidden, dim_out)

    def forward(self, x):
        # x shape: (batch_size, seq_len, input_dim)
        batch_size, seq_len = x.size(0), x.size(1)

        # GRU forward pass
        gru_out, _ = self.bigru(x)  # gru_out shape: (batch_size, seq_len, hidden_dim)

        # Use only the last time step output for classification
        last_output = gru_out[:, -1, :]  # shape: (batch_size, hidden_dim)

        # Classification
        outputs = self.linear(last_output)  # shape: (batch_size, num_classes)
        return outputs


#Federated-LSTM based Network Intrusion Detection Method for Intelligent Connected Vehicles, 2022
class LSTM(nn.Module):
    def __init__(self, dim_in, dim_out, seq, dim_hidden=128):
        super(LSTM, self).__init__()

        self.lstm_hidden_dim = dim_hidden
        self.seq = seq
        self.linear1 = nn.Linear(in_features=dim_in, out_features=dim_hidden)
        self.linear2 = nn.Linear(in_features=dim_hidden, out_features=dim_hidden)
        self.lstm = nn.LSTM(input_size=dim_hidden, hidden_size=dim_hidden, num_layers=1, bidirectional=False,
                            batch_first=True)
        self.lstm1 = nn.LSTM(input_size=dim_hidden, hidden_size=dim_hidden*2, num_layers=1, bidirectional=False,
                            batch_first=True)
        self.linear3 = nn.Linear(in_features=seq*dim_hidden*2, out_features=dim_out)

    def forward(self, x):
        batch_size = x.size(0)
        x2 = self.linear1(x)
        x2 = self.linear2(x2)
        recurrent_features, _ = self.lstm(x2)
        recurrent_features1, _ = self.lstm1(recurrent_features)
        outputs = self.linear3(recurrent_features1.contiguous().view(batch_size, -1))
        return outputs

# Convenience functions for creating GRU and LSTM models for different datasets

def create_gru_model(dataset_name, input_dim, seq_len=1, dim_hidden=64):
    """
    Factory function to create GRU models for specific datasets.

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        seq_len: Sequence length (window size)
        dim_hidden: Hidden dimension size

    Returns:
        GRU model for the specified dataset
    """
    # Dataset-specific configurations
    dataset_configs = {
        'carh': {'classes': 5},
        'cicids2017': {'classes': 8},
        'toniot': {'classes': 10}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    config = dataset_configs[dataset_name]
    num_classes = config['classes']

    model = GRU(
        dim_in=input_dim,
        dim_out=num_classes,
        lstm_num_layers=1,
        dim_hidden=dim_hidden
    )

    return model

def create_lstm_model(dataset_name, input_dim, seq_len=1, dim_hidden=128):
    """
    Factory function to create LSTM models for specific datasets.

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        seq_len: Sequence length (window size)
        dim_hidden: Hidden dimension size

    Returns:
        LSTM model for the specified dataset
    """
    # Dataset-specific configurations
    dataset_configs = {
        'carh': {'classes': 5},
        'cicids2017': {'classes': 8},
        'toniot': {'classes': 10}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    config = dataset_configs[dataset_name]
    num_classes = config['classes']

    model = LSTM(
        dim_in=input_dim,
        dim_out=num_classes,
        seq=seq_len,
        dim_hidden=dim_hidden
    )

    return model


def create_edge_cnn_model(dataset_name):
    """
    Factory function to create EdgeCNN models for specific datasets.

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        hidden_dim: Hidden dimension for CNN layers (optimized for edge computing)
        dropout: Dropout rate for regularization

    Returns:
        EdgeCNN instance configured for the dataset
    """
    # Dataset-specific configurations
    dataset_configs = {
        'carh': {'classes': 5},
        'cicids2017': {'classes': 8},
        'toniot': {'classes': 10}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    config = dataset_configs[dataset_name]
    num_classes = config['classes']

    # Create and return the configured model
    model = EdgeCNN(
        n_classes=num_classes
    )

    return model
