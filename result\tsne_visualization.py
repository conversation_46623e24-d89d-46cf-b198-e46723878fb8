"""
t-SNE 可视化工具模块。

该模块提供了对全局模型特征进行 t-SNE 可视化的功能，支持在训练完成后对分类器之前的特征进行降维可视化。
"""

import numpy as np
import matplotlib.pyplot as plt
import pickle
import os
import datetime
from typing import Dict, List, Tuple, Optional, Any
from sklearn.manifold import TSNE
from sklearn.preprocessing import MinMaxScaler
from sklearn.decomposition import PCA
import torch
from torch.utils.data import DataLoader

# 设置全局字体为 Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['mathtext.fontset'] = 'stix'  # 数学字体设置为与 Times 兼容的字体
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号


class TSNEVisualizer:
    """t-SNE 可视化器类。"""
    
    def __init__(self):
        """初始化 t-SNE 可视化器。"""
        self.features_storage = {}
        
    def store_features(self, features: np.ndarray, labels: np.ndarray, round_num: int) -> None:
        """
        存储特征数据。
        
        参数:
            features: 特征数组 (n_samples, n_features)
            labels: 标签数组 (n_samples,)
            round_num: 轮次编号
        """
        self.features_storage[round_num] = {
            'features': features,
            'labels': labels,
            'timestamp': datetime.datetime.now()
        }
        print(f"✅ 已存储第{round_num}轮的特征数据: {features.shape}")
    
    def extract_features_from_model(self,
                                   model: torch.nn.Module,
                                   data_loader: DataLoader,
                                   device: torch.device) -> Tuple[np.ndarray, np.ndarray]:
        """
        从模型中提取分类器之前的特征。

        参数:
            model: 训练好的模型
            data_loader: 数据加载器
            device: 计算设备

        返回:
            features: 特征数组 (n_samples, n_features)
            labels: 标签数组 (n_samples,)
        """
        model.eval()
        features_list = []
        labels_list = []

        with torch.no_grad():
            for data, target in data_loader:
                data, target = data.to(device), target.to(device)
                
                # 使用 return_features=True 获取特征
                try:
                    _, features = model(data, return_features=True)
                    features_np = features.cpu().numpy()
                    labels_np = target.cpu().numpy()
                    
                    features_list.append(features_np)
                    labels_list.append(labels_np)

                except TypeError:
                    print("⚠️  Warning: Model does not support return_features parameter")
                    break

        if not features_list:
            return None, None

        # 合并所有批次的数据
        all_features = np.vstack(features_list)
        all_labels = np.hstack(labels_list)

        return all_features, all_labels

    def _perform_tsne_visualization(self, test_metric: Dict[str, Any], dataset_name: str = None) -> None:
        """在第100轮时对test_metric中的features_before_classifier进行T-SNE可视化。

        Args:
            test_metric: 测试指标字典，包含features_before_classifier
            dataset_name: 数据集名称，用于确定类别标签
        """
        try:
            print(f"\n🎨 开始进行T-SNE可视化分析（第100轮）...")

            # 检查是否存在features_before_classifier
            if 'features_before_classifier' not in test_metric or test_metric['features_before_classifier'] is None:
                print("❌ 未找到features_before_classifier数据，跳过T-SNE可视化")
                return

            # 提取特征和标签
            features = np.array(test_metric['features_before_classifier'])

            # 检查是否有真实标签
            if 'y_true' in test_metric and test_metric['y_true'] is not None:
                y_true = np.array(test_metric['y_true'])
            else:
                print("❌ 未找到真实标签数据，跳过T-SNE可视化")
                return

            print(f"特征形状: {features.shape}")
            print(f"标签形状: {y_true.shape}")
            print(f"唯一标签: {np.unique(y_true)}")

            # 对特征进行归一化到[0,1]范围
            print("对特征进行归一化到[0,1]范围...")
            scaler = MinMaxScaler(feature_range=(0, 1))
            features_normalized = scaler.fit_transform(features)
            print(f"归一化后特征统计: 最小值={np.min(features_normalized):.6f}, 最大值={np.max(features_normalized):.6f}")

            # 如果特征维度太高，先进行PCA降维
            if features_normalized.shape[1] > 50:
                print(f"特征维度较高({features_normalized.shape[1]})，先使用PCA降维到50维...")
                pca = PCA(n_components=50, random_state=42)
                features_pca = pca.fit_transform(features_normalized)
                print(f"PCA后特征形状: {features_pca.shape}")
                print(f"PCA解释的方差比例: {pca.explained_variance_ratio_.sum():.3f}")
            else:
                features_pca = features_normalized

            # 执行T-SNE降维
            print("执行T-SNE降维...")
            tsne = TSNE(
                n_components=2,
                random_state=42,  # 确保结果可重现
                perplexity=min(30, len(features_pca) - 1),  # 确保perplexity不超过样本数
                n_iter=1000,
                # verbose=1,
                # learning_rate=200
            )

            features_2d = tsne.fit_transform(features_pca)

            # 对T-SNE输出的2D坐标进行归一化到[-1,1]范围
            print("对T-SNE输出坐标进行归一化到[-1,1]范围...")
            scaler_2d = MinMaxScaler(feature_range=(-1, 1))
            features_2d_normalized = scaler_2d.fit_transform(features_2d)
            print(f"T-SNE坐标归一化前范围: X[{np.min(features_2d[:, 0]):.2f}, {np.max(features_2d[:, 0]):.2f}], Y[{np.min(features_2d[:, 1]):.2f}, {np.max(features_2d[:, 1]):.2f}]")
            print(f"T-SNE坐标归一化后范围: X[{np.min(features_2d_normalized[:, 0]):.2f}, {np.max(features_2d_normalized[:, 0]):.2f}], Y[{np.min(features_2d_normalized[:, 1]):.2f}, {np.max(features_2d_normalized[:, 1]):.2f}]")


            # features_2d_normalized = features_2d

            # 创建可视化
            plt.figure() #figsize=(12, 8)

            # 获取唯一标签和对应的颜色
            unique_labels = np.unique(y_true)
            colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))

            # 根据数据集定义类别名称
            if dataset_name and dataset_name.lower() == 'cicids2017':
                class_names = ['Normal', 'Bot', 'Brute', 'DDoS', 'DoS', 'Heartbleed', 'Infiltration', 'Web']
            elif dataset_name and dataset_name.lower() == 'carh':
                class_names = ['Normal', 'Flooding', 'Fuzzing', 'Replay', 'Spoofing']  #['Normal', 'DoS', 'Fuzzy', 'Gear', 'RPM']
            elif dataset_name and dataset_name.lower() == 'toniot':
                class_names = ['Normal', 'Backdoor', 'DDoS', 'Injection', 'MITM', 'Password', 'Ransomware', 'Scanning', 'XSS', 'Other']
            else:
                class_names = [f'Class {int(label)}' for label in unique_labels]

            # 为每个类别绘制散点图
            for i, label in enumerate(unique_labels):
                mask = y_true == label
                # 使用对应的类别名称
                if dataset_name and int(label) < len(class_names):
                    label_name = class_names[int(label)]
                else:
                    label_name = f'Class {int(label)}'

                plt.scatter(
                    features_2d_normalized[mask, 0],
                    features_2d_normalized[mask, 1],
                    c=[colors[i]],
                    label=label_name,
                    alpha=0.7,
                    s=10,
                    # edgecolors='black',
                    linewidth=0.5
                )

            # 设置坐标轴刻度，以0.5为间隔，字体大小为14
            plt.xticks([-1.0, -0.5, 0.0, 0.5, 1.0], fontsize=13)
            plt.yticks([-1.0, -0.5, 0.0, 0.5, 1.0], fontsize=13)

            # plt.title('T-SNE Visualization of Features Before Classifier (Round 100)',
            #          fontsize=16, fontweight='bold')
            # plt.xlabel('T-SNE Component 1', fontsize=12)
            # plt.ylabel('T-SNE Component 2', fontsize=12)
            plt.legend(loc='upper right', fontsize=13, markerscale=2)  #bbox_to_anchor=(1.05, 1),
            # plt.grid(True, alpha=0.3)
            plt.tight_layout()

            # 保存图像
            save_dir = "tsne_visualizations"
            os.makedirs(save_dir, exist_ok=True)

            # 生成文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tsne_round50_{timestamp}.png"
            filepath = os.path.join(save_dir, filename)

            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            print(f"✅ T-SNE可视化图已保存到: {filepath}")

            # 也保存为PDF格式
            pdf_filepath = filepath.replace('.png', '.pdf')
            plt.savefig(pdf_filepath, bbox_inches='tight')
            print(f"✅ T-SNE可视化图(PDF)已保存到: {pdf_filepath}")

            # 显示图像（如果在交互环境中）
            try:
                plt.show()
            except:
                pass

            plt.close()

            # # 保存T-SNE结果数据
            # tsne_data = {
            #     'features_2d_original': features_2d.tolist(),
            #     'features_2d_normalized': features_2d_normalized.tolist(),
            #     'labels': y_true.tolist(),
            #     'unique_labels': unique_labels.tolist(),
            #     'original_feature_shape': features.shape,
            #     'normalized_feature_shape': features_normalized.shape,
            #     'tsne_params': {
            #         'random_state': 42,
            #         'perplexity': tsne.perplexity,
            #         'n_iter': 1000,
            #         'learning_rate': 200
            #     },
            #     'round': 50
            # }

            # data_filepath = os.path.join(save_dir, f"tsne_data_round50_{timestamp}.pkl")
            # with open(data_filepath, 'wb') as f:
            #     pickle.dump(tsne_data, f)
            # print(f"✅ T-SNE数据已保存到: {data_filepath}")

        except Exception as e:
            print(f"❌ T-SNE可视化失败: {str(e)}")
            import traceback
            traceback.print_exc()


# 创建全局可视化器实例
tsne_visualizer = TSNEVisualizer()


def main() -> None:
    """
    主函数，演示 t-SNE 可视化功能。
    """
    # 生成示例数据
    np.random.seed(42)
    n_samples = 1000
    n_features = 128
    n_classes = 8
    
    # 创建示例特征数据
    features = np.random.randn(n_samples, n_features)
    labels = np.random.randint(0, n_classes, n_samples)
    
    # 准备测试指标字典
    test_metric = {
        'features_before_classifier': features,
        'y_true': labels
    }
    
    # 执行 t-SNE 可视化
    visualizer = TSNEVisualizer()
    visualizer._perform_tsne_visualization(test_metric, 'cicids2017')

    print("✅ Example t-SNE visualization completed!")


if __name__ == "__main__":
    main()
