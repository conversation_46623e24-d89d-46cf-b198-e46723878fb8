#!/usr/bin/env python3
"""
实时运行联邦学习实验脚本
显示实时输出和进度
"""

import subprocess
import sys
import os
import datetime


def run_experiment_realtime(name: str, args: str):
    """实时运行单个实验并显示输出"""
    
    print(f"\n{'='*80}")
    print(f"[EXPERIMENT] {name}")
    print(f"{'='*80}")
    print(f"[TIME] {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"[COMMAND] python main_u.py {args}")
    print(f"[STATUS] Activating conda environment and starting...")
    
    # 创建批处理文件
    bat_content = f"""@echo off
chcp 65001 >nul
call conda activate dl
if %errorlevel% neq 0 (
    echo [ERROR] Failed to activate conda dl environment
    exit /b 1
)
set PYTHONIOENCODING=utf-8
echo [INFO] Conda dl environment activated
echo [INFO] Starting experiment: {name}
python main_u.py {args}
echo [INFO] Experiment completed with exit code %errorlevel%
"""
    
    temp_bat = "temp_realtime.bat"
    with open(temp_bat, "w", encoding="utf-8") as f:
        f.write(bat_content)
    
    try:
        # 启动进程
        process = subprocess.Popen(
            [temp_bat],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding="utf-8",
            errors="replace",
            universal_newlines=True,
            bufsize=1
        )
        
        output_buffer = []
        
        print(f"[RUNNING] Experiment is running... (PID: {process.pid})")
        print(f"[INFO] Real-time output:")
        print("-" * 80)
        
        # 实时读取并显示输出
        while True:
            line = process.stdout.readline()
            if line:
                line = line.rstrip()
                # 显示实时输出
                print(line)
                output_buffer.append(line)
                
                # 刷新输出缓冲区
                sys.stdout.flush()
                
            elif process.poll() is not None:
                break
        
        # 等待进程完成
        return_code = process.wait()
        
        print("-" * 80)
        print(f"[COMPLETED] {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 保存结果
        full_output = '\n'.join(output_buffer)
        with open("result.md", "a", encoding="utf-8") as f:
            f.write(f"""
# 实验: {name}

## 配置
- 命令: python main_u.py {args}
- 开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 返回码: {return_code}
- 状态: {'成功' if return_code == 0 else '失败'}

## 完整输出
```
{full_output}
```

---

""")
        
        if return_code == 0:
            print(f"[SUCCESS] ✅ {name} completed successfully!")
            return True
        else:
            print(f"[FAILED] ❌ {name} failed with return code: {return_code}")
            return False
            
    except KeyboardInterrupt:
        print(f"\n[INTERRUPTED] User interrupted the experiment")
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            try:
                process.kill()
            except:
                pass
        return False
        
    except Exception as e:
        print(f"[ERROR] Exception occurred: {e}")
        return False
        
    finally:
        # 清理临时文件
        try:
            os.remove(temp_bat)
        except:
            pass


def main():
    """主函数：运行所有实验"""
    
    print("🚀 实时联邦学习实验运行器")
    print(f"开始时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 实验配置
    experiments = [
        # CICIDS2017 + Error Feedback True
        ("CNN_Transformer_CICIDS2017_TopK_0.9", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.9 --enable_error_feedback True"),
        ("CNN_Transformer_CICIDS2017_TopK_0.8", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.8 --enable_error_feedback True"),
        ("CNN_Transformer_CICIDS2017_TopK_0.7", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.7 --enable_error_feedback True"),
        ("CNN_Transformer_CICIDS2017_TopK_0.6", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.6 --enable_error_feedback True"),
        ("CNN_Transformer_CICIDS2017_TopK_0.5", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.5 --enable_error_feedback True"),
        ("CNN_Transformer_CICIDS2017_TopK_0.4", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.4 --enable_error_feedback True"),
        ("CNN_Transformer_CICIDS2017_TopK_0.3", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.3 --enable_error_feedback True"),
        ("CNN_Transformer_CICIDS2017_TopK_0.2", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.2 --enable_error_feedback True"),
        ("CNN_Transformer_CICIDS2017_TopK_0.1", "--model cnn_transformer --dataset cicids2017 --topk_ratio 0.1 --enable_error_feedback True"),
        
        # CARH + Error Feedback False
        ("CNN_Transformer_CARH_TopK_0.9_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.9 --enable_error_feedback False"),
        ("CNN_Transformer_CARH_TopK_0.8_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.8 --enable_error_feedback False"),
        ("CNN_Transformer_CARH_TopK_0.7_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.7 --enable_error_feedback False"),
        ("CNN_Transformer_CARH_TopK_0.6_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.6 --enable_error_feedback False"),
        ("CNN_Transformer_CARH_TopK_0.5_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.5 --enable_error_feedback False"),
        ("CNN_Transformer_CARH_TopK_0.4_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.4 --enable_error_feedback False"),
        ("CNN_Transformer_CARH_TopK_0.3_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.3 --enable_error_feedback False"),
        ("CNN_Transformer_CARH_TopK_0.2_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.2 --enable_error_feedback False"),
        ("CNN_Transformer_CARH_TopK_0.1_EF_False", "--model cnn_transformer --dataset carh --topk_ratio 0.1 --enable_error_feedback False"),
        
        # CARH + Error Feedback True
        ("CNN_Transformer_CARH_TopK_0.9_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.9 --enable_error_feedback True"),
        ("CNN_Transformer_CARH_TopK_0.8_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.8 --enable_error_feedback True"),
        ("CNN_Transformer_CARH_TopK_0.7_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.7 --enable_error_feedback True"),
        ("CNN_Transformer_CARH_TopK_0.6_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.6 --enable_error_feedback True"),
        ("CNN_Transformer_CARH_TopK_0.5_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.5 --enable_error_feedback True"),
        ("CNN_Transformer_CARH_TopK_0.4_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.4 --enable_error_feedback True"),
        ("CNN_Transformer_CARH_TopK_0.3_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.3 --enable_error_feedback True"),
        ("CNN_Transformer_CARH_TopK_0.2_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.2 --enable_error_feedback True"),
        ("CNN_Transformer_CARH_TopK_0.1_EF_True", "--model cnn_transformer --dataset carh --topk_ratio 0.1 --enable_error_feedback True"),
    ]
    
    print(f"[INFO] Total experiments: {len(experiments)}")
    
    # 询问是否运行所有实验
    choice = input(f"\n[QUESTION] Run all {len(experiments)} experiments? (y/n): ").strip().lower()
    if choice not in ['y', 'yes']:
        print("[CANCELLED] User cancelled the experiments")
        return
    
    # 创建结果文件
    with open("result.md", "w", encoding="utf-8") as f:
        f.write(f"""# 联邦学习实验结果

生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
运行环境: conda dl

## 实验概述
总共27个CNN_Transformer模型实验

""")
    
    # 运行实验
    success_count = 0
    start_time = datetime.datetime.now()
    
    for i, (name, args) in enumerate(experiments, 1):
        print(f"\n[PROGRESS] {i}/{len(experiments)} experiments")
        print(f"[REMAINING] Estimated {len(experiments)-i} experiments left")
        
        if run_experiment_realtime(name, args):
            success_count += 1
        
        # 显示当前进度
        elapsed = datetime.datetime.now() - start_time
        avg_time_per_exp = elapsed.total_seconds() / i
        remaining_time = avg_time_per_exp * (len(experiments) - i)
        
        print(f"[PROGRESS] Success rate: {success_count}/{i}")
        print(f"[TIME] Elapsed: {elapsed}")
        print(f"[ESTIMATE] Remaining time: {datetime.timedelta(seconds=int(remaining_time))}")
        
        # 询问是否继续（如果失败）
        if success_count < i:
            choice = input(f"[QUESTION] Continue with next experiment? (y/n): ").strip().lower()
            if choice not in ['y', 'yes']:
                print("[STOPPED] User chose to stop experiments")
                break
    
    # 最终总结
    total_time = datetime.datetime.now() - start_time
    print(f"\n{'='*80}")
    print(f"[SUMMARY] All experiments completed!")
    print(f"[RESULTS] Success: {success_count}/{len(experiments)}")
    print(f"[TIME] Total time: {total_time}")
    print(f"[FILE] Results saved to: result.md")
    print(f"{'='*80}")


if __name__ == "__main__":
    main()
