#!/usr/bin/env python3
"""
Centralized Training for Intrusion Detection Models

This script implements centralized training (non-federated) for intrusion detection models
using the complete dataset without partitioning across clients.
"""

import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import time
import sys
import os
import json
from datetime import datetime
from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, classification_report

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.models.models import create_intrusion_detection_model, create_cnn_transformer_model, weight_init
from centralized_dataset import load_dataset, get_dataset_info

def calculate_detailed_metrics(model, test_loader, device, num_classes):
    """
    计算模型的详细指标：precision, recall, F1, False alarm rate, confusion matrix

    Args:
        model: 训练好的模型
        test_loader: 测试数据加载器
        device: 计算设备
        num_classes: 类别数量

    Returns:
        dict: 包含所有指标的字典
    """
    model.eval()

    all_predictions = []
    all_targets = []

    # 获取所有预测和真实标签
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            pred = output.argmax(dim=1)

            all_predictions.extend(pred.cpu().numpy())
            all_targets.extend(target.cpu().numpy())

    # 转换为numpy数组
    y_true = np.array(all_targets)
    y_pred = np.array(all_predictions)

    # 计算precision, recall, F1
    precision, recall, f1, support = precision_recall_fscore_support(
        y_true, y_pred, average=None, zero_division=0
    )

    # 计算宏平均和微平均
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='macro', zero_division=0
    )
    precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='micro', zero_division=0
    )

    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred, labels=list(range(num_classes)))

    # 计算False Alarm Rate (FPR) for each class
    false_alarm_rates = []
    for i in range(num_classes):
        # True Negatives: correctly predicted as not class i
        tn = np.sum(cm) - (np.sum(cm[i, :]) + np.sum(cm[:, i]) - cm[i, i])
        # False Positives: incorrectly predicted as class i
        fp = np.sum(cm[:, i]) - cm[i, i]

        # False Alarm Rate = FP / (FP + TN)
        if (fp + tn) > 0:
            far = fp / (fp + tn)
        else:
            far = 0.0
        false_alarm_rates.append(far)

    # 计算整体False Alarm Rate (平均)
    avg_false_alarm_rate = np.mean(false_alarm_rates)

    # 计算准确率
    accuracy = np.sum(y_pred == y_true) / len(y_true)

    return {
        'accuracy': accuracy,
        'precision_per_class': precision,
        'recall_per_class': recall,
        'f1_per_class': f1,
        'support_per_class': support,
        'precision_macro': precision_macro,
        'recall_macro': recall_macro,
        'f1_macro': f1_macro,
        'precision_micro': precision_micro,
        'recall_micro': recall_micro,
        'f1_micro': f1_micro,
        'false_alarm_rates': false_alarm_rates,
        'avg_false_alarm_rate': avg_false_alarm_rate,
        'confusion_matrix': cm,
        'y_true': y_true,
        'y_pred': y_pred
    }

def print_dataset_analysis(train_dataset, test_dataset, dataset_name):
    """
    Print detailed dataset analysis.

    Args:
        train_dataset: Training dataset
        test_dataset: Test dataset
        dataset_name: Name of the dataset
    """
    print(f"\n{'='*80}")
    print(f"📊 CENTRALIZED DATASET ANALYSIS - {dataset_name.upper()}")
    print(f"{'='*80}")

    # Extract targets
    train_targets = np.array(train_dataset.targets)
    test_targets = np.array(test_dataset.targets)

    # Get unique classes
    unique_classes = sorted(set(train_targets))
    num_classes = len(unique_classes)

    print(f"📈 Dataset Overview:")
    print(f"   Total Training Samples: {len(train_dataset):,}")
    print(f"   Total Test Samples: {len(test_dataset):,}")
    print(f"   Number of Classes: {num_classes}")
    print(f"   Classes: {unique_classes}")

    # Calculate class distribution
    train_class_counts = {cls: np.sum(train_targets == cls) for cls in unique_classes}
    test_class_counts = {cls: np.sum(test_targets == cls) for cls in unique_classes}

    print(f"\n📊 Class Distribution:")
    print(f"   Training: {train_class_counts}")
    print(f"   Test: {test_class_counts}")

    # Calculate class percentages
    print(f"\n📈 Class Percentages:")
    print(f"{'Class':<8} {'Train Count':<12} {'Train %':<10} {'Test Count':<12} {'Test %':<10}")
    print(f"{'-'*8} {'-'*12} {'-'*10} {'-'*12} {'-'*10}")

    for cls in unique_classes:
        train_count = train_class_counts[cls]
        test_count = test_class_counts[cls]
        train_pct = 100.0 * train_count / len(train_dataset)
        test_pct = 100.0 * test_count / len(test_dataset)

        print(f"{cls:<8} {train_count:<12,} {train_pct:<10.2f} {test_count:<12,} {test_pct:<10.2f}")

    # Data balance analysis
    train_counts = list(train_class_counts.values())
    test_counts = list(test_class_counts.values())

    train_balance = np.std(train_counts) / np.mean(train_counts) if np.mean(train_counts) > 0 else 0
    test_balance = np.std(test_counts) / np.mean(test_counts) if np.mean(test_counts) > 0 else 0

    print(f"\n⚖️  Data Balance Analysis:")
    print(f"   Training data balance (CV): {train_balance:.3f}")
    print(f"   Test data balance (CV): {test_balance:.3f}")
    print(f"   Balance interpretation: 0.0=perfectly balanced, >0.5=highly imbalanced")

    print(f"{'='*80}\n")

def train_centralized_model(model, train_loader, test_loader, args, device):
    """
    Train the model using centralized approach.
    
    Args:
        model: The neural network model
        train_loader: Training data loader
        test_loader: Test data loader
        args: Training arguments
        device: Training device (cuda/cpu)
    
    Returns:
        tuple: (final_train_loss, final_train_acc, final_test_loss, final_test_acc)
    """
    print(f"\n🚀 Starting Centralized Training")
    print(f"   Model: {args.model}")
    print(f"   Dataset: {args.dataset}")
    print(f"   Device: {device}")
    print(f"   Epochs: {args.epochs}")
    print(f"   Batch Size: {args.bs}")
    print(f"   Learning Rate: {args.lr}")
    
    # Setup optimizer and loss function
    optimizer = optim.Adam(model.parameters(), lr=args.lr, 
                          betas=(args.beta1, args.beta2), 
                          eps=args.eps, 
                          weight_decay=args.weight_decay)
    
    loss_func = nn.CrossEntropyLoss()
    
    # Training history
    train_losses = []
    train_accuracies = []
    test_losses = []
    test_accuracies = []
    
    best_test_acc = 0.0
    
    print(f"\n{'='*80}")
    print(f"📈 CENTRALIZED TRAINING PROGRESS")
    print(f"{'='*80}")
    
    for epoch in range(args.epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        correct_train = 0
        total_train = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = loss_func(output, target)
            
            loss.backward()

            optimizer.step()
            
            train_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct_train += pred.eq(target.view_as(pred)).sum().item()
            total_train += target.size(0)
        
        # Calculate training metrics
        avg_train_loss = train_loss / len(train_loader)
        train_acc = 100. * correct_train / total_train
        
        # Evaluation phase
        model.eval()
        test_loss = 0.0
        correct_test = 0
        total_test = 0
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(device), target.to(device)
                output = model(data)
                test_loss += loss_func(output, target).item()
                pred = output.argmax(dim=1, keepdim=True)
                correct_test += pred.eq(target.view_as(pred)).sum().item()
                total_test += target.size(0)
        
        # Calculate test metrics
        avg_test_loss = test_loss / len(test_loader)
        test_acc = 100. * correct_test / total_test
        
        # Update best accuracy
        if test_acc > best_test_acc:
            best_test_acc = test_acc
        
        # Store metrics
        train_losses.append(avg_train_loss)
        train_accuracies.append(train_acc)
        test_losses.append(avg_test_loss)
        test_accuracies.append(test_acc)
        
        # Print progress
        print(f"Epoch {epoch+1:3d}/{args.epochs} | "
              f"Train Loss: {avg_train_loss:.6f} | "
              f"Train Acc: {train_acc:6.2f}% | "
              f"Test Loss: {avg_test_loss:.6f} | "
              f"Test Acc: {test_acc:6.2f}% | "
              f"Best: {best_test_acc:6.2f}%")
    
    print(f"{'='*80}")
    print(f"🎉 CENTRALIZED TRAINING COMPLETED")
    print(f"   Final Train Loss: {train_losses[-1]:.6f}")
    print(f"   Final Train Acc: {train_accuracies[-1]:.2f}%")
    print(f"   Final Test Loss: {test_losses[-1]:.6f}")
    print(f"   Final Test Acc: {test_accuracies[-1]:.2f}%")
    print(f"   Best Test Acc: {best_test_acc:.2f}%")
    print(f"{'='*80}")
    
    return train_losses[-1], train_accuracies[-1], test_losses[-1], test_accuracies[-1]

def main():
    """Main function for centralized training."""
    # Create argument parser for centralized training
    import argparse
    parser = argparse.ArgumentParser(description='Centralized Training for Intrusion Detection')

    # Add centralized training specific arguments
    parser.add_argument('--model', type=str, default='cnn_transformer', help='Model type')
    parser.add_argument('--dataset', type=str, default='cicids2017', choices=['cicids2017', 'carh', 'toniot'], help='Dataset name')
    parser.add_argument('--epochs', type=int, default=50, help='Number of training epochs')
    parser.add_argument('--bs', type=int, default=128, help='Batch size for training')
    parser.add_argument('--lr', type=float, default=0.0002, help='Learning rate')
    parser.add_argument('--beta1', type=float, default=0.9, help='Adam beta1')
    parser.add_argument('--beta2', type=float, default=0.999, help='Adam beta2')
    parser.add_argument('--eps', type=float, default=0., help='Adam epsilon')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--window_size', type=int, default=1, help='Window size for temporal features')
    parser.add_argument('--model_dropout', type=float, default=0.2, help='Model dropout rate')

    # CNN Transformer specific parameters
    parser.add_argument('--cnn_transformer_cnn_layers', type=int, default=2, help='Number of CNN layers for CNN Transformer model')
    parser.add_argument('--cnn_transformer_transformer_layers', type=int, default=2, help='Number of transformer layers for CNN Transformer model')
    parser.add_argument('--cnn_transformer_d_model', type=int, default=128, help='Model dimension for CNN Transformer model')
    parser.add_argument('--cnn_transformer_nhead', type=int, default=2, help='Number of attention heads for CNN Transformer model')
    parser.add_argument('--cnn_transformer_cnn_channels', type=int, default=64, help='Number of CNN channels for CNN Transformer model')
    parser.add_argument('--cnn_transformer_hidden_dim', type=int, default=256, help='Hidden dimension for CNN Transformer model')
    parser.add_argument('--cnn_transformer_projection_dim', type=int, default=64, help='Projection dimension for CNN Transformer model')

    parser.add_argument('--save_model', type=bool, default=False, help='Save trained model')

    # Parse arguments
    args = parser.parse_args()
    
    # Device configuration
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"\n🖥️  DEVICE INFORMATION:")
    print(f"   Device: {device}")
    if torch.cuda.is_available():
        print(f"   GPU ID: {torch.cuda.current_device()}")
        print(f"   GPU Name: {torch.cuda.get_device_name()}")
        print(f"   GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print(f"   CUDA Available: ✅ Yes")
    else:
        print(f"   CUDA Available: ❌ No")
    
    # Load dataset (without partitioning)
    print(f"\n📊 LOADING DATASET: {args.dataset.upper()}")

    # Get dataset info
    dataset_info = get_dataset_info(args.dataset)
    print(f"   Description: {dataset_info['description']}")
    print(f"   Features: {dataset_info['features']}")
    print(f"   Classes: {dataset_info['classes']}")

    # Load dataset
    window_size = getattr(args, 'window_size', 1)
    train_dataset, test_dataset = load_dataset(args.dataset, window_size=window_size, train_ratio=0.7)
    
    print(f"   Total Training Samples: {len(train_dataset):,}")
    print(f"   Total Test Samples: {len(test_dataset):,}")

    # Print detailed dataset analysis
    print_dataset_analysis(train_dataset, test_dataset, args.dataset)

    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=args.bs, shuffle=False, num_workers=0)
    test_loader = DataLoader(test_dataset, batch_size=args.bs, shuffle=False, num_workers=0)

    print(f"📦 DATA LOADERS CREATED:")
    print(f"   Training Batches: {len(train_loader):,}")
    print(f"   Test Batches: {len(test_loader):,}")
    print(f"   Batch Size: {args.bs}")
    print(f"   Shuffle Training: ❌ No")
    print(f"   Shuffle Test: ❌ No")
    
    # Create model
    print(f"\n🏗️  CREATING MODEL: {args.model}")
    
    if args.model == 'cnn_transformer':
        # Get dataset-specific parameters
        dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
        input_dim = dataset_features[args.dataset]
        seq_len = args.window_size

        model = create_cnn_transformer_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            cnn_layers=getattr(args, 'cnn_transformer_cnn_layers', 2),
            transformer_layers=getattr(args, 'cnn_transformer_transformer_layers', 2),
            d_model=getattr(args, 'cnn_transformer_d_model', 128),
            nhead=getattr(args, 'cnn_transformer_nhead', 4),
            cnn_channels=getattr(args, 'cnn_transformer_cnn_channels', 64),
            dropout=args.model_dropout,
            hidden_dim=getattr(args, 'cnn_transformer_hidden_dim', 256),
            projection_dim=getattr(args, 'cnn_transformer_projection_dim', None)
        ).to(device)
    else:
        raise ValueError(f"Unsupported model: {args.model}")
    
    # Apply weight initialization
    model.apply(weight_init)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"   Total Parameters: {total_params:,}")
    print(f"   Trainable Parameters: {trainable_params:,}")
    print(f"   Model Size: {total_params * 4 / 1024**2:.2f} MB")
    
    # Start training
    start_time = time.time()
    
    final_train_loss, final_train_acc, final_test_loss, final_test_acc = train_centralized_model(
        model, train_loader, test_loader, args, device
    )
    
    end_time = time.time()
    training_time = end_time - start_time
    
    print(f"\n⏱️  TRAINING TIME: {training_time:.2f} seconds ({training_time/60:.2f} minutes)")

    # Calculate detailed metrics for final test
    print(f"\n📊 CALCULATING DETAILED METRICS...")

    # Get dataset-specific number of classes
    dataset_classes = {'carh': 5, 'cicids2017': 8, 'toniot': 10}
    num_classes = dataset_classes.get(args.dataset, 8)

    detailed_metrics = calculate_detailed_metrics(model, test_loader, device, num_classes)

    print(f"\n{'='*80}")
    print(f"📈 FINAL TEST METRICS - DETAILED ANALYSIS")
    print(f"{'='*80}")

    # Print overall metrics
    print(f"\n🎯 OVERALL METRICS:")
    print(f"   Accuracy: {detailed_metrics['accuracy']:.4f}")
    print(f"   Precision (Macro): {detailed_metrics['precision_macro']:.4f}")
    print(f"   Recall (Macro): {detailed_metrics['recall_macro']:.4f}")
    print(f"   F1-Score (Macro): {detailed_metrics['f1_macro']:.4f}")
    print(f"   False Alarm Rate (Avg): {detailed_metrics['avg_false_alarm_rate']:.4f}")


    # Print per-class metrics
    print(f"\n📊 PER-CLASS METRICS:")
    print(f"{'Class':<8} {'Precision':<12} {'Recall':<12} {'F1-Score':<12} {'FAR':<12} {'Support':<10}")
    print(f"{'-'*8} {'-'*12} {'-'*12} {'-'*12} {'-'*12} {'-'*10}")

    for i in range(num_classes):
        if i < len(detailed_metrics['precision_per_class']):
            precision = detailed_metrics['precision_per_class'][i]
            recall = detailed_metrics['recall_per_class'][i]
            f1 = detailed_metrics['f1_per_class'][i]
            far = detailed_metrics['false_alarm_rates'][i]
            support = detailed_metrics['support_per_class'][i]

            print(f"{i:<8} {precision:<12.4f} {recall:<12.4f} {f1:<12.4f} {far:<12.4f} {support:<10}")

    # Print confusion matrix
    print(f"\n🔢 CONFUSION MATRIX:")
    print(detailed_metrics['confusion_matrix'])

    print(f"{'='*80}")

    # Save results
    results = {
        'experiment_info': {
            'model': args.model,
            'dataset': args.dataset,
            'training_type': 'centralized',
            'timestamp': datetime.now().isoformat(),
            'device': str(device),
            'total_params': total_params,
            'trainable_params': trainable_params
        },
        'training_config': {
            'epochs': args.epochs,
            'batch_size': args.bs,
            'learning_rate': args.lr,
            'beta1': args.beta1,
            'beta2': args.beta2,
            'eps': args.eps,
            'weight_decay': args.weight_decay
        },
        'dataset_info': {
            'train_samples': len(train_dataset),
            'test_samples': len(test_dataset),
            'train_batches': len(train_loader),
            'test_batches': len(test_loader)
        },
        'final_results': {
            'train_loss': final_train_loss,
            'train_accuracy': final_train_acc,
            'test_loss': final_test_loss,
            'test_accuracy': final_test_acc,
            'training_time_seconds': training_time,
            'training_time_minutes': training_time / 60
        },
        'detailed_metrics': {
            'accuracy': float(detailed_metrics['accuracy']),
            'precision_macro': float(detailed_metrics['precision_macro']),
            'recall_macro': float(detailed_metrics['recall_macro']),
            'f1_macro': float(detailed_metrics['f1_macro']),
            'avg_false_alarm_rate': float(detailed_metrics['avg_false_alarm_rate']),
            'precision_per_class': [float(x) for x in detailed_metrics['precision_per_class']],
            'recall_per_class': [float(x) for x in detailed_metrics['recall_per_class']],
            'f1_per_class': [float(x) for x in detailed_metrics['f1_per_class']],
            'false_alarm_rates': [float(x) for x in detailed_metrics['false_alarm_rates']],
            'support_per_class': [int(x) for x in detailed_metrics['support_per_class']],
            'confusion_matrix': detailed_metrics['confusion_matrix'].tolist()
        }
    }

    # Save results to JSON
    results_filename = f"centralized_results_{args.model}_{args.dataset}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_filename, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"📊 Results saved to: {results_filename}")

    # Save model if requested
    if args.save_model:
        model_path = f"centralized_{args.model}_{args.dataset}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pth"
        torch.save(model.state_dict(), model_path)
        print(f"💾 Model saved to: {model_path}")

    # Print comparison summary
    print(f"\n🔍 CENTRALIZED VS FEDERATED COMPARISON SUMMARY:")
    print(f"   Centralized Training Completed Successfully")
    print(f"   Final Test Accuracy: {final_test_acc:.2f}%")
    print(f"   Training Time: {training_time/60:.2f} minutes")
    print(f"   Total Samples Used: {len(train_dataset):,}")
    print(f"   No Data Partitioning: All data used for single model")
    print(f"   No Communication Overhead: Direct training")
    print(f"\n💡 Use this as baseline to compare with federated learning results!")

if __name__ == '__main__':
    main()
