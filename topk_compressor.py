"""
Top-K Compressor for Federated Learning

This module implements a Top-K gradient/weight compression algorithm for federated learning.
The compressor selects the top-k most important parameters based on their magnitude.
"""

import torch
import numpy as np
from typing import Dict, Tuple, Optional, Union
from collections import OrderedDict


class TopkCompressor:
    """
    Top-K Compressor for gradient/weight compression in federated learning.
    
    This compressor selects the top-k parameters with the largest absolute values,
    effectively reducing communication overhead by transmitting only the most
    important parameters.
    """
    
    def __init__(self, compress_ratio: float = 1.0):
        """
        Initialize the Top-K compressor.
        
        Args:
            compress_ratio (float): Compression ratio (0 < ratio <= 1.0).
                                  - 1.0: No compression (send all parameters)
                                  - 0.1: Send only top 10% of parameters
        """
        self.compress_ratio = compress_ratio
        self.residual_memory = {}  # Store residual gradients for error feedback
        
    def compress(self, tensor_dict: Dict[str, torch.Tensor], 
                use_error_feedback: bool = True) -> Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor]]:
        """
        Compress the input tensor dictionary using Top-K selection.
        
        Args:
            tensor_dict (Dict[str, torch.Tensor]): Dictionary of tensors to compress
            use_error_feedback (bool): Whether to use error feedback mechanism
            
        Returns:
            Tuple containing:
                - compressed_dict: Dictionary with compressed tensors
                - indices_dict: Dictionary with indices of selected elements
        """
        if self.compress_ratio >= 1.0:
            # No compression needed
            indices_dict = {}
            for name, tensor in tensor_dict.items():
                indices_dict[name] = torch.arange(tensor.numel(), device=tensor.device)
            return tensor_dict, indices_dict
        
        compressed_dict = {}
        indices_dict = {}
        
        for name, tensor in tensor_dict.items():
            # Add residual from previous round if using error feedback
            if use_error_feedback and name in self.residual_memory:
                tensor = tensor + self.residual_memory[name]
            
            # Flatten tensor for easier processing
            original_shape = tensor.shape
            flat_tensor = tensor.flatten()
            
            # Calculate number of elements to keep
            total_elements = flat_tensor.numel()
            k = max(1, int(total_elements * self.compress_ratio))
            
            # Get top-k indices based on absolute values
            _, top_indices = torch.topk(torch.abs(flat_tensor), k, largest=True)
            
            # Create compressed tensor
            compressed_flat = torch.zeros_like(flat_tensor)
            compressed_flat[top_indices] = flat_tensor[top_indices]
            
            # Reshape back to original shape
            compressed_tensor = compressed_flat.reshape(original_shape)
            
            # Store compressed tensor and indices
            compressed_dict[name] = compressed_tensor
            indices_dict[name] = top_indices
            
            # Update residual memory for error feedback
            if use_error_feedback:
                self.residual_memory[name] = tensor - compressed_tensor
        
        return compressed_dict, indices_dict
    
    def decompress(self, compressed_dict: Dict[str, torch.Tensor],
                  indices_dict: Dict[str, torch.Tensor],
                  original_shapes: Optional[Dict[str, torch.Size]] = None) -> Dict[str, torch.Tensor]:
        """
        Decompress the compressed tensor dictionary.

        This function efficiently reconstructs tensors by:
        1. Creating zero tensors with original shapes
        2. Using index_put_ to place compressed values at specified indices
        3. All unselected positions remain zero (proper Top-K decompression)

        Args:
            compressed_dict (Dict[str, torch.Tensor]): Compressed tensors
            indices_dict (Dict[str, torch.Tensor]): Indices of selected elements
            original_shapes (Optional[Dict[str, torch.Size]]): Original tensor shapes

        Returns:
            Dict[str, torch.Tensor]: Decompressed tensors with zeros at unselected positions
        """
        if self.compress_ratio >= 1.0:
            # No decompression needed
            return compressed_dict

        decompressed_dict = {}

        for name, compressed_tensor in compressed_dict.items():
            if name in indices_dict and indices_dict[name] is not None:
                indices = indices_dict[name]

                # Use original shape if provided, otherwise use compressed tensor shape
                if original_shapes and name in original_shapes:
                    target_shape = original_shapes[name]
                else:
                    target_shape = compressed_tensor.shape

                # Create zero tensor and flatten for efficient indexing
                decompressed_tensor = torch.zeros(size=target_shape,
                                                dtype=compressed_tensor.dtype,
                                                device=compressed_tensor.device).view(-1)

                # Extract values at the selected indices from compressed tensor
                if len(indices) > 0:
                    flat_compressed = compressed_tensor.view(-1)
                    selected_values = flat_compressed[indices]

                    # Use index_put_ for efficient assignment: places values at indices
                    decompressed_tensor = decompressed_tensor.index_put_([indices], selected_values, accumulate=True)

                # Reshape back to target shape
                decompressed_dict[name] = decompressed_tensor.view(target_shape)
            else:
                # No indices available, return compressed tensor as-is
                decompressed_dict[name] = compressed_tensor

        return decompressed_dict
    
    def get_compression_stats(self, tensor_dict: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        Get compression statistics for the given tensor dictionary.
        
        Args:
            tensor_dict (Dict[str, torch.Tensor]): Input tensors
            
        Returns:
            Dict[str, float]: Compression statistics
        """
        total_elements = sum(tensor.numel() for tensor in tensor_dict.values())
        compressed_elements = int(total_elements * self.compress_ratio)
        
        stats = {
            'total_parameters': total_elements,
            'compressed_parameters': compressed_elements,
            'compression_ratio': self.compress_ratio,
            'reduction_ratio': 1.0 - self.compress_ratio,
            'bandwidth_savings': f"{(1.0 - self.compress_ratio) * 100:.1f}%"
        }
        
        return stats
    
    def reset_memory(self):
        """Reset the residual memory for error feedback."""
        self.residual_memory.clear()
    
    def update_compression_ratio(self, new_ratio: float):
        """
        Update the compression ratio.
        
        Args:
            new_ratio (float): New compression ratio (0 < ratio <= 1.0)
        """
        if 0 < new_ratio <= 1.0:
            self.compress_ratio = new_ratio
        else:
            raise ValueError(f"Compression ratio must be in (0, 1], got {new_ratio}")


def create_topk_compressor(compress_ratio: float = 1.0) -> TopkCompressor:
    """
    Factory function to create a Top-K compressor.
    
    Args:
        compress_ratio (float): Compression ratio (0 < ratio <= 1.0)
        
    Returns:
        TopkCompressor: Configured Top-K compressor instance
    """
    return TopkCompressor(compress_ratio=compress_ratio)


# Utility functions for model parameter and update compression
def compress_model_params(model_params: Dict[str, torch.Tensor],
                         compressor: TopkCompressor,
                         use_error_feedback: bool = True) -> Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor]]:
    """
    Compress model parameters using the provided compressor.

    Args:
        model_params (Dict[str, torch.Tensor]): Model parameters to compress
        compressor (TopkCompressor): Compressor instance
        use_error_feedback (bool): Whether to use error feedback mechanism

    Returns:
        Tuple containing compressed parameters and indices
    """
    return compressor.compress(model_params, use_error_feedback=use_error_feedback)


def compress_model_updates(model_updates: Dict[str, torch.Tensor],
                          compressor: TopkCompressor,
                          use_error_feedback: bool = True) -> Tuple[Dict[str, torch.Tensor], Dict[str, torch.Tensor]]:
    """
    Compress model updates (parameter differences) using the provided compressor.

    This function is specifically designed for compressing model update differences
    (current_params - initial_params) in federated learning scenarios.

    Args:
        model_updates (Dict[str, torch.Tensor]): Model parameter updates to compress
        compressor (TopkCompressor): Compressor instance
        use_error_feedback (bool): Whether to use error feedback mechanism

    Returns:
        Tuple containing:
            - compressed_updates: Dictionary of compressed model updates
            - indices_dict: Dictionary of indices for selected update positions
    """
    return compressor.compress(model_updates, use_error_feedback=use_error_feedback)


def decompress_model_params(compressed_params: Dict[str, torch.Tensor],
                           indices_dict: Dict[str, torch.Tensor],
                           compressor: TopkCompressor) -> Dict[str, torch.Tensor]:
    """
    Decompress model parameters using the provided compressor.

    Args:
        compressed_params (Dict[str, torch.Tensor]): Compressed parameters
        indices_dict (Dict[str, torch.Tensor]): Indices of selected elements
        compressor (TopkCompressor): Compressor instance

    Returns:
        Dict[str, torch.Tensor]: Decompressed parameters
    """
    return compressor.decompress(compressed_params, indices_dict)


def decompress_model_updates(compressed_updates: Dict[str, torch.Tensor],
                            indices_dict: Dict[str, torch.Tensor],
                            compressor: TopkCompressor) -> Dict[str, torch.Tensor]:
    """
    Decompress model updates using the provided compressor.

    This function decompresses model update differences that were compressed
    using compress_model_updates().

    Args:
        compressed_updates (Dict[str, torch.Tensor]): Compressed model updates
        indices_dict (Dict[str, torch.Tensor]): Indices of selected update positions
        compressor (TopkCompressor): Compressor instance

    Returns:
        Dict[str, torch.Tensor]: Decompressed model updates
    """
    return compressor.decompress(compressed_updates, indices_dict)


# def apply_compressed_updates_to_model(model_params: Dict[str, torch.Tensor],
#                                      compressed_updates: Dict[str, torch.Tensor],
#                                      indices_dict: Dict[str, torch.Tensor],
#                                      compressor: TopkCompressor) -> Dict[str, torch.Tensor]:
#     """
#     Apply compressed model updates to model parameters.

#     This function decompresses the updates and applies them to the model parameters:
#     new_params = old_params + decompressed_updates

#     Args:
#         model_params (Dict[str, torch.Tensor]): Current model parameters
#         compressed_updates (Dict[str, torch.Tensor]): Compressed model updates
#         indices_dict (Dict[str, torch.Tensor]): Indices of selected update positions
#         compressor (TopkCompressor): Compressor instance

#     Returns:
#         Dict[str, torch.Tensor]: Updated model parameters
#     """
#     # Decompress the updates
#     decompressed_updates = decompress_model_updates(compressed_updates, indices_dict, compressor)

#     # Apply updates to model parameters
#     updated_params = {}
#     for name in model_params:
#         if name in decompressed_updates:
#             updated_params[name] = model_params[name] + decompressed_updates[name]
#         else:
#             updated_params[name] = model_params[name].clone()

#     return updated_params



