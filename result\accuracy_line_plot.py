"""
准确率折线图绘制工具模块。

该模块提供了绘制多种方法准确率随轮次变化的折线图功能。
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional

# 设置全局字体为 Times New Roman
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['mathtext.fontset'] = 'stix'  # 数学字体设置为与 Times 兼容的字体
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 定义颜色列表
COLORS = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
          '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']


def plot_line_chart(
    data: Dict[str, List[float]],
    x_values: List[int] = None,
    title: str = "Accuracy vs. Rounds",
    xlabel: str = "Rounds",
    ylabel: str = "Accuracy",
    figsize: Tuple[float, float] = (6.4, 4.8),
    markers: List[str] = None,
    linestyles: List[str] = None,
    save_path: Optional[str] = None,
    ylim: Optional[Tuple[float, float]] = (0.0, 1.0),
    grid: bool = True,
    legend_loc: str = 'lower right',
    start_from_zero: bool = True
) -> None:
    """
    绘制折线图，展示多种方法的性能随轮次的变化。
    
    参数:
        data: 字典，格式为 {方法: [值列表]}，值范围为0到1
        x_values: x轴值，如果为None则使用0到n-1的序列
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        figsize: 图表大小，默认为 matplotlib 的标准大小 (6.4, 4.8)
        markers: 标记样式列表
        linestyles: 线条样式列表
        save_path: 保存路径，如果为None则不保存
        ylim: y轴范围，格式为(ymin, ymax)，默认为(0.0, 1.0)
        grid: 是否显示网格
        legend_loc: 图例位置
        start_from_zero: 是否从第0轮开始（准确率为0），默认为True
    
    示例:
        data = {
            "Method 1": [0.652, 0.705, 0.758, 0.782, 0.801, 0.815, 0.823, 0.830, 0.835, 0.840],
            "Method 2": [0.683, 0.721, 0.765, 0.798, 0.823, 0.841, 0.856, 0.862, 0.868, 0.872],
            "Method 3": [0.628, 0.684, 0.729, 0.761, 0.785, 0.802, 0.817, 0.825, 0.831, 0.838],
            "Method 4": [0.701, 0.745, 0.789, 0.823, 0.847, 0.865, 0.878, 0.885, 0.891, 0.895]
        }
        plot_line_chart(data)
    """
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 获取方法
    methods = list(data.keys())
    
    # 如果没有提供x轴值，则使用从0开始的索引
    if x_values is None:
        # 使用第一个方法的数据长度作为参考
        x_values = list(range(0, len(data[methods[0]])))
        if start_from_zero:
            # 添加第0轮
            x_values = [0] + x_values
    
    # 如果没有提供标记样式，则使用默认值
    if markers is None:
        markers = ['o', 's', '^', 'D', 'v', '>', '<', 'p', '*', 'h']
    
    # 如果没有提供线条样式，则使用默认值
    if linestyles is None:
        linestyles = ['-', '--', '-.', ':']
    
    # 绘制折线图
    for i, method in enumerate(methods):
        plot_data = data[method]
        plot_x = x_values
        
        # 如果需要从第0轮开始，且x_values不包含0，则在数据前添加0
        if start_from_zero and 0 not in x_values and len(x_values) > 0 and x_values[0] > 0:
            plot_data = [0.0] + plot_data
            plot_x = [0] + list(x_values)
        elif start_from_zero and len(x_values) > 0 and x_values[0] == 0 and len(plot_data) < len(x_values):
            plot_data = [0.0] + plot_data
        
        plt.plot(plot_x, plot_data, 
                 marker=markers[i % len(markers)], 
                 linestyle=linestyles[i % len(linestyles)],
                 color=COLORS[i % len(COLORS)],
                 linewidth=2,
                 markersize=8,
                 label=method)
    
    # 设置图表属性
    plt.xlabel(xlabel, fontsize=14, family='Times New Roman')
    plt.ylabel(ylabel, fontsize=14, family='Times New Roman')
    # plt.title(title, fontsize=14, family='Times New Roman')
    
    # 设置网格
    if grid:
        plt.grid(linestyle='--', alpha=0.7)
    
    # 设置y轴范围和刻度
    if ylim:
        plt.ylim(ylim)
        # 设置y轴主刻度和次刻度
        major_ticks = np.arange(ylim[0], ylim[1] + 0.01, 0.2)
        minor_ticks = np.arange(ylim[0], ylim[1] + 0.01, 0.1)
        plt.yticks(major_ticks, fontsize=16, family='Times New Roman')
        ax = plt.gca()
        ax.set_yticks(minor_ticks, minor=True)
        ax.tick_params(which='minor', length=4, color='gray', width=1.0)
        
        # 为主刻度和次刻度添加网格线
        ax.grid(which='major', axis='y', linestyle='--', alpha=0.7)
        ax.grid(which='minor', axis='y', linestyle=':', alpha=0.4)
    
    # 设置x轴范围，从0开始顶头
    if len(x_values) > 0:
        plt.xlim(0, max(x_values))
    
    # 添加图例
    plt.legend(fontsize=16, loc=legend_loc, prop={'family': 'Times New Roman'})
    
    # 设置x轴刻度
    if len(x_values) > 10:
        # 如果x轴值过多，则只显示部分刻度
        step = max(1, len(x_values) // 10)
        plt.xticks(x_values[::step], fontsize=16, family='Times New Roman')
    else:
        plt.xticks(x_values, fontsize=16, family='Times New Roman')
    
    # 保存图表
    if save_path:
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")
    
    # 显示图表
    plt.tight_layout()
    plt.show()


def main() -> None:
    """
    主函数，演示折线图绘制功能。
    """
    # 示例数据
    line_data = {
        "Method 1": [0.652, 0.705, 0.758, 0.782, 0.801, 0.815, 0.823, 0.830, 0.835, 0.840],
        "Method 2": [0.683, 0.721, 0.765, 0.798, 0.823, 0.841, 0.856, 0.862, 0.868, 0.872],
        "Method 3": [0.628, 0.684, 0.729, 0.761, 0.785, 0.802, 0.817, 0.825, 0.831, 0.838],
        "Method 4": [0.701, 0.745, 0.789, 0.823, 0.847, 0.865, 0.878, 0.885, 0.891, 0.895]
    }
    
    # 使用从1开始的轮次，但图表从0开始显示（第0轮准确率为0）
    rounds = list(range(1, 11))
    
    # 绘制折线图
    plot_line_chart(line_data, x_values=rounds, save_path="line_chart_example.png")


if __name__ == "__main__":
    main()
