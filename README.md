# 增强联邦学习 (EFL) 框架

一个具有高级剪枝功能、多种模型架构和入侵检测应用的综合联邦学习框架。

## 🎯 项目概述

本仓库扩展了原始 Sub-FedAvg 实现，为入侵检测和网络安全应用增加了增强功能。该框架支持多种神经网络架构、灵活的剪枝策略，并在网络安全数据集上进行全面评估。

### 核心特性

- **🔧 灵活的剪枝控制**: 简化的剪枝系统，支持轻松模式切换
- **🧠 多种模型架构**: 支持 GRU、LSTM、TCN+Transformer、MLP 和 LeNet5
- **🛡️ 网络安全专注**: 专门针对网络入侵检测任务
- **📊 多数据集支持**: CarH、CICIDS2017、ToNIoT 和传统机器学习数据集
- **⚡ 高级优化器**: 可配置参数的 Adam 优化器
- **🔄 非IID数据处理**: 包括 Dirichlet 在内的多种数据分布策略


## 🚀 快速开始

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/MMorafah/Sub-FedAvg.git
cd Sub-FedAvg
```

2. **环境配置**
```bash
# 创建 conda 环境
conda create -n efl python=3.8
conda activate efl

# 安装依赖
pip install torch torchvision numpy pandas scikit-learn matplotlib seaborn
```

3. **数据集准备**
```bash
# 将数据集放置在 src/RawData/ 目录下
# 支持的格式: carh.csv, cicids2017.csv, toniot.csv
```

### 基础使用

#### 入侵检测 - 默认剪枝模式
```bash
# CarH 数据集上的 GRU 模型
python main_u.py --dataset carh --model gru

# CICIDS2017 数据集上的 LSTM 模型
python main_u.py --dataset cicids2017 --model lstm

# ToNIoT 数据集上的 TCN+Transformer 模型
python main_u.py --dataset toniot --model intrusion_detection
```

#### 标准联邦平均 (无剪枝)
```bash
# 禁用剪枝进行基准对比
python main_u.py --dataset carh --model gru --disable_pruning
python main_u.py --dataset cicids2017 --model lstm --disable_pruning
```

#### 高级配置
```bash
# 自定义联邦学习参数
python main_u.py --dataset carh --model gru \
    --rounds 100 --num_users 10 --frac 0.3 \
    --local_ep 5 --local_bs 32 --lr 0.001

# 非独立同分布数据
python main_u.py --dataset cicids2017 --model lstm \
    --noniid --dirichlet --alpha 0.1

# 序列数据分析
python main_u.py --dataset toniot --model lstm \
    --window_size 10

# 剪枝参数调优
python main_u.py --dataset carh --model intrusion_detection \
    --pruning_percent 15 --pruning_target 40 \
    --dist_thresh 0.0001 --acc_thresh 50
```

## 📋 系统要求

- **Python**: 3.8+
- **PyTorch**: 1.8+
- **NumPy**: 1.19+
- **Pandas**: 1.3+
- **Scikit-learn**: 1.0+
- **Matplotlib**: 3.3+
- **Seaborn**: 0.11+

## 🏗️ 项目结构

```
EFL/
├── main_u.py                 # 主训练脚本
├── script_u.sh              # 训练脚本示例
├── src/                      # 源代码目录
│   ├── client/              # 客户端实现
│   │   ├── client_u.py      # 剪枝客户端
│   │   └── client_standard.py  # 标准客户端
│   ├── data/                # 数据处理模块
│   │   └── data.py          # 数据加载和分割
│   ├── models/              # 模型定义
│   │   ├── models.py        # 标准模型 (GRU, LSTM, etc.)
│   │   └── unstructured.py  # 剪枝模型
│   ├── pruning/             # 剪枝算法
│   │   └── unstructured.py  # 非结构化剪枝
│   ├── sub_fedavg/          # 联邦平均算法
│   │   ├── sub_fedavg_u.py  # 剪枝联邦平均
│   │   └── fedavg_standard.py  # 标准联邦平均
│   ├── utils/               # 工具函数
│   │   └── options_u.py     # 参数配置
│   ├── configs/             # 数据集配置
│   │   ├── carh.py          # CarH 数据集配置
│   │   ├── CICIDS2017.py    # CICIDS2017 配置
│   │   └── ToNIoT.py        # ToNIoT 配置
│   └── RawData/             # 原始数据存储
│       ├── carh.csv         # CarH 数据集
│       ├── cicids2017.csv   # CICIDS2017 数据集
│       └── toniot.csv       # ToNIoT 数据集
└── README.md                # 项目文档
```

## ⚙️ 参数配置

### 1. 联邦学习参数
```bash
--rounds 300              # 训练轮数
--num_users 10            # 客户端数量
--frac 0.5                # 每轮参与的客户端比例
--local_ep 5              # 本地训练轮数
--local_bs 64             # 本地批次大小
--lr 0.0002               # 学习率
```

### 2. 模型选择
```bash
--model intrusion_detection    # 模型类型选择
# 可选项:
#   - intrusion_detection: TCN+Transformer (推荐用于入侵检测)
#   - gru: GRU 循环神经网络
#   - lstm: LSTM 循环神经网络
#   - mlp: 多层感知机
#   - lenet5: LeNet5 (用于图像数据)
```

### 3. 数据集配置
```bash
--dataset cicids2017      # 数据集选择
# 支持的数据集:
#   - carh: Car Hacking 数据集 (11特征, 5类别)
#   - cicids2017: CICIDS2017 网络入侵检测 (78特征, 8类别)
#   - toniot: ToN-IoT 物联网安全 (42特征, 10类别)
#   - mnist, cifar10, cifar100: 传统机器学习数据集

--window_size 1           # 序列窗口大小 (用于时序分析)
```

### 4. 数据分布策略
```bash
--noniid                  # 启用非独立同分布
--dirichlet               # 使用 Dirichlet 分布
--alpha 0.5               # Dirichlet 浓度参数 (越小越不均匀)
--shard                   # 基于分片的非IID
--label                   # 基于标签的非IID
```
### 5. 剪枝控制
```bash
--disable_pruning         # 禁用剪枝 (使用标准 FedAvg)
# 默认: 启用剪枝模式

# 剪枝参数 (仅在剪枝模式下有效):
--pruning_percent 40      # 每轮剪枝百分比 (0-100)
--pruning_target 30       # 总体剪枝目标百分比 (0-100)
--dist_thresh 0.0001      # 掩码差异阈值
--acc_thresh 50           # 应用剪枝的精度阈值
```

### 6. Adam 优化器参数
```bash
--beta1 0.9               # Adam beta1 参数
--beta2 0.999             # Adam beta2 参数
--eps 0.0                 # Adam epsilon 参数
--weight_decay 0.0        # 权重衰减 (L2正则化)
```

### 7. 其他配置
```bash
--gpu 0                   # GPU ID (-1 表示使用 CPU)
--seed 1                  # 随机种子
--is_print                # 启用详细输出
--print_freq 100          # 打印频率
```
## 📊 支持的数据集

### 网络安全数据集

| 数据集 | 特征数 | 类别数 | 描述 | 应用场景 |
|--------|--------|--------|------|----------|
| **CarH** | 11 | 5 | 汽车CAN总线入侵检测 | 车联网安全 |
| **CICIDS2017** | 78 | 8 | 网络入侵检测基准 | 网络安全 |
| **ToNIoT** | 42 | 10 | 物联网安全数据集 | IoT安全 |

### 传统机器学习数据集
- **MNIST**: 手写数字识别 (28×28, 10类)
- **CIFAR-10**: 自然图像分类 (32×32×3, 10类)
- **CIFAR-100**: 自然图像分类 (32×32×3, 100类)

## 🔬 实验示例

### 基准对比实验
```bash
# 在 CarH 数据集上对比不同模型
python main_u.py --dataset carh --model gru --rounds 100
python main_u.py --dataset carh --model lstm --rounds 100
python main_u.py --dataset carh --model intrusion_detection --rounds 100

# 对比剪枝和标准模式
python main_u.py --dataset carh --model gru --rounds 100
python main_u.py --dataset carh --model gru --rounds 100 --disable_pruning
```

### 非IID数据实验
```bash
# 不同 alpha 值的 Dirichlet 分布
python main_u.py --dataset cicids2017 --model lstm --noniid --dirichlet --alpha 0.1  # 高度非IID
python main_u.py --dataset cicids2017 --model lstm --noniid --dirichlet --alpha 1.0  # 中等非IID
python main_u.py --dataset cicids2017 --model lstm --noniid --dirichlet --alpha 10.0 # 接近IID
```

### 序列长度影响实验
```bash
# 不同窗口大小对 LSTM 性能的影响
python main_u.py --dataset toniot --model lstm --window_size 1
python main_u.py --dataset toniot --model lstm --window_size 5
python main_u.py --dataset toniot --model lstm --window_size 10
python main_u.py --dataset toniot --model lstm --window_size 20
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情。


