"""
Car Hacking Challenge Dataset处理模块。
用于读取和预处理Pre_submit_D.csv数据集，执行标签编码，并提取指定比例的数据。
"""
import os
import pandas as pd
import numpy as np
import traceback
from sklearn.preprocessing import LabelEncoder
from typing import Dict, Tuple, List, Any, Optional, Union

def process_carh_data(data_path: str, sample_ratio: float = 0.3,
                      save_csv: bool = True, output_path: Optional[str] = None,
                      random_seed: int = 42, normal_samples: Optional[int] = None) -> pd.DataFrame:
    """处理Car Hacking Challenge数据集。

    Args:
        data_path: Pre_submit_D.csv文件的路径
        sample_ratio: 从攻击类别中按比例提取的比例，默认为0.3
        save_csv: 是否保存处理后的数据框到CSV文件，默认为True
        output_path: 输出CSV文件的路径，默认为flgoasyn/benchmark/RawData/carh.csv
        random_seed: 随机种子，用于确保抽样结果可复现，默认为42
        normal_samples: Normal类别要提取的具体样本数量，如果为None则按sample_ratio比例提取

    Returns:
        处理后的DataFrame
    """
    print(f"处理Car Hacking Challenge数据集，文件路径: {data_path}")
    print(f"样本提取比例: {sample_ratio}")
    print(f"随机种子: {random_seed}")

    # 设置随机种子
    np.random.seed(random_seed)

    # 设置默认输出路径
    if output_path is None:
        # 获取项目根目录
        root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
        output_path = os.path.join(root_dir, "src/RawData/carh.csv") 

    try:
        # 读取CSV文件
        print("读取Pre_submit_D.csv文件...")
        df = pd.read_csv(data_path, low_memory=False)
        print(f"原始数据形状: {df.shape}")
        print(f"原始列名: {list(df.columns)}")

        # 验证数据格式 - 期望6列数据
        if len(df.columns) != 6:
            raise ValueError(f"期望6列数据，但实际有{len(df.columns)}列")

        # 重命名列以确保一致性
        expected_columns = ['Timestamp', 'Arbitration_ID', 'DLC', 'Data', 'Class', 'SubClass']
        df.columns = expected_columns

        # 删除Class列，保留SubClass作为标签
        print("删除Class列，保留SubClass作为标签")
        df = df.drop(columns=['Class'])

        # 处理Data列 - 分割为Data0-Data7
        print("处理Data列，分割为Data0-Data7...")

        # 分割Data列（按空格分割）
        data_split = df['Data'].str.split(' ', expand=True)

        # 确保有8列数据，不足的补'00'
        for i in range(8):
            if i < data_split.shape[1]:
                df[f'Data{i}'] = data_split[i].fillna('00')
            else:
                df[f'Data{i}'] = '00'

        # 删除原始Data列
        df = df.drop(columns=['Data'])

        # 处理Arbitration_ID列 - 将16进制转换为10进制
        print("处理Arbitration_ID列，将16进制转换为10进制...")
        df['CAN_ID'] = df['Arbitration_ID'].apply(
            lambda x: int(str(x), 16) if pd.notna(x) and str(x).strip() != '' else 0
        )
        df = df.drop(columns=['Arbitration_ID'])

        # 处理Data0-Data7列 - 将16进制转换为10进制
        print("处理Data0-Data7列，将16进制转换为10进制...")
        data_columns = [f'Data{i}' for i in range(8)]
        for col in data_columns:
            df[col] = df[col].fillna('00').astype(str)
            # 确保是2位16进制数
            df[col] = df[col].apply(lambda x: x.zfill(2) if len(x) < 2 else x[:2])
            # 转换16进制为10进制
            df[col] = df[col].apply(
                lambda x: int(x, 16) if all(c in '0123456789abcdefABCDEF' for c in x) else 0
            )

        # 处理DLC列 - 确保是整数
        print("处理DLC列...")
        df['DLC'] = pd.to_numeric(df['DLC'], errors='coerce').fillna(0).astype(int)

        # 处理Timestamp列 - 确保是数值类型
        print("处理Timestamp列...")
        df['Timestamp'] = pd.to_numeric(df['Timestamp'], errors='coerce').fillna(0)

        # 处理SubClass列 - 标签编码，确保Normal为0
        print("处理SubClass列，进行标签编码...")

        # 先使用LabelEncoder获取所有唯一类别
        label_encoder = LabelEncoder()
        original_labels = label_encoder.fit_transform(df['SubClass'])

        # 创建类别映射字典
        class_mapping = {label: idx for idx, label in enumerate(label_encoder.classes_)}
        reverse_mapping = {idx: label for label, idx in class_mapping.items()}

        # 检查是否存在Normal类别（不区分大小写）
        normal_class = None
        for class_name in label_encoder.classes_:
            if isinstance(class_name, str) and class_name.lower() == 'normal':
                normal_class = class_name
                break
            elif not isinstance(class_name, str) and str(class_name).lower() == 'normal':
                normal_class = class_name
                break

        # 如果找到Normal类别，创建新的映射确保它为0
        if normal_class is not None:
            normal_idx = class_mapping[normal_class]

            # 创建新的映射关系
            new_mapping = {}
            idx_counter = 1  # 从1开始为非Normal类别编号

            # 将Normal类别映射为0
            new_mapping[normal_idx] = 0

            # 为其他类别重新分配索引
            for old_idx in range(len(label_encoder.classes_)):
                if old_idx != normal_idx:
                    new_mapping[old_idx] = idx_counter
                    idx_counter += 1

            # 应用新映射到标签
            df['Label'] = [new_mapping[idx] for idx in original_labels]

            # 创建更新后的类别到索引的映射用于显示
            updated_class_mapping = {reverse_mapping[old_idx]: new_idx
                                   for old_idx, new_idx in new_mapping.items()}

            print("类别编码映射:")
            for class_name, idx in updated_class_mapping.items():
                print(f"  {class_name} -> {idx}")
        else:
            # 如果没有找到Normal类别，直接使用LabelEncoder的结果
            df['Label'] = original_labels
            print("警告: 未找到'Normal'类别，使用默认LabelEncoder编码")
            print("类别编码映射:")
            for class_name, idx in class_mapping.items():
                print(f"  {class_name} -> {idx}")

        # 删除原始SubClass列
        df = df.drop(columns=['SubClass'])

        # 重新排列列的顺序
        column_order = ['Timestamp', 'CAN_ID', 'DLC'] + [f'Data{i}' for i in range(8)] + ['Label']
        df = df[column_order]

        print(f"数据预处理完成，形状: {df.shape}")
        print(f"类别分布:\n{df['Label'].value_counts().sort_index()}")

        # 根据标签类别提取数据
        print("按类别提取数据...")
        if normal_samples is not None:
            print(f"  Normal类别(标签0): 提取固定数量 {normal_samples}")
            print(f"  攻击类别: 按比例 {sample_ratio:.1%} 提取")
        else:
            print(f"  所有类别: 按比例 {sample_ratio:.1%} 提取")

        sampled_data = pd.DataFrame()
        for label in sorted(df['Label'].unique()):
            label_data = df[df['Label'] == label]

            # 对Normal类别（标签为0）使用固定数量提取
            if label == 0 and normal_samples is not None:
                # Normal类别使用固定数量
                available_samples = len(label_data)
                sample_size = min(normal_samples, available_samples)

                if sample_size > 0:
                    sampled_label_data = label_data.sample(n=sample_size, random_state=random_seed)
                    sampled_data = pd.concat([sampled_data, sampled_label_data], ignore_index=True)
                    print(f"  标签 {label} (Normal): 从{available_samples}条中提取{sample_size}条 (固定数量)")
                else:
                    print(f"  标签 {label} (Normal): 无可用样本")
            else:
                # 其他攻击类别使用比例提取
                sample_size = int(len(label_data) * sample_ratio)
                if sample_size > 0:
                    sampled_label_data = label_data.sample(n=sample_size, random_state=random_seed)
                    sampled_data = pd.concat([sampled_data, sampled_label_data], ignore_index=True)
                    print(f"  标签 {label}: 从{len(label_data)}条中提取{sample_size}条 (按比例 {sample_ratio:.1%})")
                else:
                    print(f"  标签 {label}: 样本数量太少，跳过")

        # 随机打乱数据，使用seed确保可复现
        print(f"使用随机种子 {random_seed} 对数据进行随机打乱")
        sampled_data = sampled_data.sample(frac=1, random_state=random_seed).reset_index(drop=True)
        print("数据已随机打乱")

        print(f"最终提取的数据形状: {sampled_data.shape}")
        print(f"最终类别分布:\n{sampled_data['Label'].value_counts().sort_index()}")

        # 保存处理后的数据框到CSV文件
        if save_csv and not sampled_data.empty:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                print(f"创建输出目录: {output_dir}")

            # 保存数据框到CSV文件
            sampled_data.to_csv(output_path, index=False)
            print(f"处理后的数据已保存到: {output_path}")

        return sampled_data

    except Exception as e:
        print(f"处理数据时出错: {str(e)}")
        traceback.print_exc()
        raise


if __name__ == "__main__":
    # 如果直接运行此脚本，处理数据并保存到CSV
    import argparse

    parser = argparse.ArgumentParser(description='处理Car Hacking Challenge数据集并保存为CSV')
    parser.add_argument('--data_path', type=str,
                        default='D:/experiment/PFL/flgoasyn/benchmark/RawData/CAN20/Car_Hacking_Challenge_Dataset_rev20Mar2021/0_Preliminary/1_Submission/Pre_submit_S.csv',
                        help='Pre_submit_D.csv文件的路径')
    parser.add_argument('--output_path', type=str, default=None,
                        help='输出CSV文件的路径，默认为flgoasyn/benchmark/RawData/carh.csv')
    parser.add_argument('--sample_ratio', type=float, default=0.1,
                        help='从攻击类别中按比例提取的比例，默认为0.1')
    parser.add_argument('--normal_samples', type=int, default=10000,
                        help='Normal类别要提取的具体样本数量，如果不指定则按sample_ratio比例提取')
    parser.add_argument('--random_seed', type=int, default=42,
                        help='随机种子，用于确保抽样结果可复现，默认为42')

    args = parser.parse_args()

    # 处理数据并保存
    process_carh_data(
        data_path=args.data_path,
        sample_ratio=args.sample_ratio,
        save_csv=True,
        output_path=args.output_path,
        random_seed=args.random_seed,
        normal_samples=args.normal_samples
    )

    print("数据处理完成！")