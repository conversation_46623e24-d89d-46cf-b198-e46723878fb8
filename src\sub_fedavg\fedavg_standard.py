import copy
import torch
import numpy as np
import sys
import os

# Add project root to path for topk_compressor import
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from topk_compressor import TopkCompressor, decompress_model_updates, decompress_model_params

def FedAvg_standard(w_glob, w_locals, num_samples=None, use_model_updates=False, topk_ratio=0.5):
    """
    Standard FedAvg aggregation without pruning.

    Implements the standard federated averaging algorithm that computes
    the weighted average of client model parameters or aggregates model updates.
    Now supports Top-K compression and model update mode.

    Args:
        w_glob: Global model state dictionary
        w_locals: List of local model state dictionaries from clients or compressed parameter dicts
        num_samples: List of number of samples per client (for weighted averaging)
                    If None, uses uniform weighting
        use_model_updates: Whether the w_locals contain model updates instead of full parameters

    Returns:
        Updated global model state dictionary
    """
    if len(w_locals) == 0:
        return w_glob

    # Check if we're dealing with compressed parameters
    is_compressed = isinstance(w_locals[0], dict) and 'compressed' in w_locals[0]

    if is_compressed:
        return FedAvg_with_compression(w_glob, w_locals, num_samples, use_model_updates, topk_ratio)
    else:
        return FedAvg_original(w_glob, w_locals, num_samples, use_model_updates)


def FedAvg_original(w_glob, w_locals, num_samples=None, use_model_updates=False):
    """
    Original FedAvg implementation for uncompressed parameters.

    Args:
        w_glob: Global model state dictionary
        w_locals: List of local model parameters or updates
        num_samples: List of number of samples per client
        use_model_updates: Whether w_locals contain model updates instead of full parameters
    """
    if use_model_updates:
        # Model updates mode: aggregate updates and apply to global model
        return FedAvg_model_updates(w_glob, w_locals, num_samples)
    else:
        # Traditional mode: aggregate full parameters
        return FedAvg_full_parameters(w_glob, w_locals, num_samples)


def FedAvg_full_parameters(w_glob, w_locals, num_samples=None):
    """Traditional FedAvg with full parameter aggregation."""
    # Initialize the aggregated weights with the first client's weights
    w_avg = copy.deepcopy(w_locals[0])

    # If no sample counts provided, use uniform weighting
    if num_samples is None:
        num_samples = [1.0] * len(w_locals)

    
    # Normalize weights to sum to 1
    total_samples = sum(num_samples)
    weights = [n / total_samples for n in num_samples]

    # Aggregate parameters using weighted average
    for key in w_avg.keys():
        # Initialize with zeros
        w_avg[key] = torch.zeros_like(w_avg[key])

        # Handle different parameter types
        if 'num_batches_tracked' in key:
            # For BatchNorm tracking counters, take the maximum value
            # This represents the maximum number of batches seen by any client
            max_tracked = w_locals[0][key].clone()
            for i, w_local in enumerate(w_locals):
                max_tracked = torch.max(max_tracked, w_local[key])
            w_avg[key] = max_tracked
        else:
            # Weighted sum for regular parameters (weights, biases)
            for i, w_local in enumerate(w_locals):
                w_avg[key] += w_local[key] * weights[i]

    return w_avg


def FedAvg_model_updates(w_glob, w_locals_updates, num_samples=None):
    """
    FedAvg with model updates aggregation.

    Args:
        w_glob: Global model state dictionary
        w_locals_updates: List of model updates from clients
        num_samples: List of number of samples per client

    Returns:
        Updated global model state dictionary
    """
    # Start from global model
    w_updated = copy.deepcopy(w_glob)


    # num_samples = None  #强制均衡
    # If no sample counts provided, use uniform weighting
    if num_samples is None:
        num_samples = [1.0] * len(w_locals_updates)

    # Normalize weights to sum to 1
    total_samples = sum(num_samples)
    weights = [n / total_samples for n in num_samples]

    # Aggregate updates
    for key in w_updated.keys():
        # Initialize aggregated update
        aggregated_update = torch.zeros_like(w_updated[key])

        # Handle different parameter types
        if 'num_batches_tracked' in key:
            # For BatchNorm tracking counters, take the maximum value (not cumulative)
            max_tracked = w_updated[key].clone()
            for i, w_update in enumerate(w_locals_updates):
                if key in w_update:
                    # For tracking counters, we want the maximum count seen
                    client_total = w_updated[key] + w_update[key]
                    max_tracked = torch.max(max_tracked, client_total)

            # Replace with maximum tracked value
            w_updated[key] = max_tracked
        else:
            # Regular parameter handling (Float type)
            # Weighted sum of updates
            for i, w_update in enumerate(w_locals_updates):
                if key in w_update:
                    aggregated_update += w_update[key] * weights[i]

            # Apply aggregated update to global model
            w_updated[key] = w_updated[key] + aggregated_update

    return w_updated

def FedAvg_standard_simple(w_locals):
    """
    Simplified standard FedAvg aggregation with uniform weighting.
    
    Args:
        w_locals: List of local model state dictionaries from clients
    
    Returns:
        Averaged model state dictionary
    """
    if len(w_locals) == 0:
        return {}
    
    # Initialize with the first client's weights
    w_avg = copy.deepcopy(w_locals[0])
    
    # Average all parameters
    for key in w_avg.keys():
        # Initialize with zeros
        w_avg[key] = torch.zeros_like(w_avg[key])
        
        # Sum all client parameters
        for w_local in w_locals:
            w_avg[key] += w_local[key]
        
        # Take average
        w_avg[key] = w_avg[key] / len(w_locals)
    
    return w_avg

def update_global_model(global_model, aggregated_weights):
    """
    Update global model with aggregated weights.
    
    Args:
        global_model: Global model to update
        aggregated_weights: Aggregated weights from FedAvg
    
    Returns:
        Updated global model
    """
    global_model.load_state_dict(aggregated_weights)
    return global_model

def distribute_global_model(global_model, client_models):
    """
    Distribute global model weights to all client models.
    
    Args:
        global_model: Global model with updated weights
        client_models: List of client models to update
    
    Returns:
        List of updated client models
    """
    global_state_dict = global_model.state_dict()
    
    for client_model in client_models:
        client_model.load_state_dict(copy.deepcopy(global_state_dict))
    
    return client_models

def compute_model_difference(model1_state, model2_state):
    """
    Compute the L2 norm difference between two model states.
    
    Args:
        model1_state: First model state dictionary
        model2_state: Second model state dictionary
    
    Returns:
        L2 norm of the difference
    """
    diff_norm = 0.0
    
    for key in model1_state.keys():
        if key in model2_state:
            diff = model1_state[key] - model2_state[key]
            diff_norm += torch.norm(diff).item() ** 2
    
    return np.sqrt(diff_norm)

def get_model_size(model_state):
    """
    Get the total number of parameters in a model.
    
    Args:
        model_state: Model state dictionary
    
    Returns:
        Total number of parameters
    """
    total_params = 0
    
    for key in model_state.keys():
        total_params += model_state[key].numel()
    
    return total_params

def print_model_stats(model_state, model_name="Model"):
    """
    Print statistics about a model.
    
    Args:
        model_state: Model state dictionary
        model_name: Name of the model for printing
    """
    total_params = get_model_size(model_state)
    
    print(f"\n--- {model_name} Statistics ---")
    print(f"Total parameters: {total_params:,}")
    
    for key, param in model_state.items():
        print(f"{key}: {param.shape} ({param.numel():,} params)")
    
    print(f"--- End {model_name} Statistics ---\n")


def FedAvg_with_compression(w_glob, w_locals_compressed, num_samples=None, use_model_updates=False, topk_ratio=0.5):
    """
    FedAvg aggregation with Top-K compression support.

    Fixed version that properly handles sparse parameter updates from TopK compression.
    Only updates parameter positions that were actually transmitted by clients,
    preserving the global model's values for untransmitted positions.
    Supports both full parameters and model updates mode.

    Args:
        w_glob: Global model state dictionary
        w_locals_compressed: List of compressed parameter dictionaries
        num_samples: List of number of samples per client
        use_model_updates: Whether the compressed data contains model updates

    Returns:
        Updated global model state dictionary
    """
    if len(w_locals_compressed) == 0:
        return w_glob

    # If no sample counts provided, use uniform weighting
    if num_samples is None:
        num_samples = [1.0] * len(w_locals_compressed)

    # Normalize weights to sum to 1
    total_samples = sum(num_samples)
    weights = [n / total_samples for n in num_samples]

    # Initialize aggregated weights - START FROM GLOBAL MODEL, NOT ZEROS
    w_avg = copy.deepcopy(w_glob)

    # Check if any client used compression
    any_compressed = any(client_data.get('compressed', False) for client_data in w_locals_compressed)

    if not any_compressed:
        # No compression used, fall back to original method
        w_locals_params = [client_data['params'] for client_data in w_locals_compressed]
        return FedAvg_original(w_glob, w_locals_params, num_samples, use_model_updates)

    # Handle compressed aggregation - FIXED LOGIC
    if use_model_updates:
        # Model updates mode: aggregate compressed updates and apply to global model
        return FedAvg_compressed_updates(w_glob, w_locals_compressed, num_samples, topk_ratio)
    else:
        # Full parameters mode: aggregate compressed parameters
        return FedAvg_compressed_parameters(w_glob, w_locals_compressed, weights, topk_ratio)


def FedAvg_compressed_parameters(w_glob, w_locals_compressed, weights, topk_ratio=0.5):
    """
    Handle compressed full parameters aggregation using TopK decompression and proper sparse vector aggregation.

    This function correctly handles Top-K compressed parameters by:
    1. Decompressing each client's sparse parameters to full tensors (with zeros at missing positions)
    2. Performing weighted aggregation of the decompressed parameters
    3. Only updating positions that were transmitted by at least one client

    Args:
        w_glob: Global model state dictionary
        w_locals_compressed: List of compressed parameters from clients
        weights: List of weights for each client

    Returns:
        Updated global model state dictionary
    """
    w_avg = copy.deepcopy(w_glob)

    # Step 1: Decompress all client parameters using dedicated utility function
    decompressed_params_list = []
    temp_compressor = TopkCompressor(compress_ratio=topk_ratio)  # Use same ratio as clients

    for client_data in w_locals_compressed:
        compressed_params = client_data['params']
        indices_dict = client_data.get('indices', {})

        # Use dedicated utility function from topk_compressor.py
        decompressed_params = decompress_model_params(
            compressed_params,
            indices_dict,
            temp_compressor
        )
        decompressed_params_list.append(decompressed_params)

    # Step 2: Aggregate decompressed parameters with weighting
    for key in w_avg.keys():
        # Handle different parameter types
        if 'num_batches_tracked' in key:
            # For BatchNorm tracking counters, take the maximum value
            # This represents the maximum number of batches seen by any client
            max_tracked = w_avg[key].clone()
            for i, decompressed_params in enumerate(decompressed_params_list):
                if key in decompressed_params:
                    max_tracked = torch.max(max_tracked, decompressed_params[key])
            w_avg[key] = max_tracked
        else:
            # For regular parameters: weighted aggregation with position tracking
            param_shape = w_avg[key].shape
            device = w_avg[key].device  # Get device from the parameter
            flat_accumulated = torch.zeros(w_avg[key].numel(), device=device)
            flat_weight_count = torch.zeros(w_avg[key].numel(), device=device)

            # Accumulate weighted parameters from all clients
            for i, decompressed_params in enumerate(decompressed_params_list):
                if key in decompressed_params:
                    flat_client_param = decompressed_params[key].flatten()

                    # Find positions that were actually transmitted (non-zero after decompression)
                    transmitted_mask = (torch.abs(flat_client_param) > 1e-10)
                    transmitted_indices = torch.where(transmitted_mask)[0]

                    if len(transmitted_indices) > 0:
                        # Accumulate weighted parameters only at transmitted positions
                        flat_accumulated[transmitted_indices] += flat_client_param[transmitted_indices] * weights[i]
                        flat_weight_count[transmitted_indices] += weights[i]

            # Update only positions that received updates from at least one client
            update_mask = (flat_weight_count > 0)
            if update_mask.any():
                # Normalize by total weight for each position
                flat_accumulated[update_mask] = flat_accumulated[update_mask] / flat_weight_count[update_mask]

                # Update global model: replace transmitted positions, keep others unchanged
                flat_global = w_avg[key].flatten()
                flat_global[update_mask] = flat_accumulated[update_mask]
                w_avg[key] = flat_global.reshape(param_shape)
            # Positions not updated by any client retain their global model values

    return w_avg


def FedAvg_compressed_updates(w_glob, w_locals_compressed, num_samples=None, topk_ratio=0.5):
    """
    Handle compressed model updates aggregation using TopK decompression and proper sparse vector aggregation.

    This function correctly handles Top-K compressed model updates by:
    1. Decompressing each client's sparse updates to full tensors (with zeros at missing positions)
    2. Performing weighted aggregation of the decompressed updates
    3. Applying the aggregated updates to the global model

    Args:
        w_glob: Global model state dictionary
        w_locals_compressed: List of compressed model updates from clients
        num_samples: List of number of samples per client for weighted averaging

    Returns:
        Updated global model state dictionary
    """
    w_updated = copy.deepcopy(w_glob)

    # If no sample counts provided, use uniform weighting
    if num_samples is None:
        num_samples = [1.0] * len(w_locals_compressed)

    # Calculate weights based on data amount: weight_i = num_samples_i / sum(num_samples_all)
    total_samples = sum(num_samples)
    weights = [n / total_samples for n in num_samples]

    # Verify weights sum to 1 (for debugging)
    weights_sum = sum(weights)
    if abs(weights_sum - 1.0) > 1e-6:
        print(f"Warning: Weights sum to {weights_sum:.6f}, expected 1.0")

    # Debug information for small number of clients
    if len(num_samples) <= 10:
        print(f"🗜️ Compressed Updates Aggregation - Data amounts: {num_samples}, Weights: {[f'{w:.4f}' for w in weights]}")

    # Step 1: Decompress all client updates using dedicated utility function
    decompressed_updates_list = []
    temp_compressor = TopkCompressor(compress_ratio=topk_ratio)  # Use same ratio as clients

    for client_data in w_locals_compressed:
        compressed_updates = client_data['params']
        indices_dict = client_data.get('indices', {})

        # Use dedicated utility function from topk_compressor.py
        decompressed_updates = decompress_model_updates(
            compressed_updates,
            indices_dict,
            temp_compressor
        )
        decompressed_updates_list.append(decompressed_updates)

    # Step 2: Aggregate decompressed updates with data-based weighting
    aggregated_updates = {}

    for key in w_updated.keys():
        # Handle different parameter types
        if 'num_batches_tracked' in key:
            # For BatchNorm tracking counters, take the maximum value (not cumulative)
            max_tracked = w_updated[key].clone()
            for i, decompressed_updates in enumerate(decompressed_updates_list):
                if key in decompressed_updates:
                    # For tracking counters, we want the maximum count seen
                    client_total = w_updated[key] + decompressed_updates[key]
                    max_tracked = torch.max(max_tracked, client_total)

            # Replace with maximum tracked value
            w_updated[key] = max_tracked
        else:
            # Initialize aggregated update for regular parameters
            aggregated_updates[key] = torch.zeros_like(w_updated[key])

            # Weighted sum of decompressed updates
            for i, decompressed_updates in enumerate(decompressed_updates_list):
                if key in decompressed_updates:
                    # Add weighted update: aggregated += client_update * weight
                    aggregated_updates[key] += decompressed_updates[key] * weights[i]

            # Apply aggregated update to global model
            w_updated[key] = w_updated[key] + aggregated_updates[key]

    return w_updated
