"""
混淆矩阵绘制工具模块。

该模块提供了绘制混淆矩阵的功能，支持归一化、自定义颜色映射和保存功能。
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Tuple, Optional

# 设置全局字体为 Times
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['mathtext.fontset'] = 'stix'  # 数学字体设置为与 Times 兼容的字体
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def plot_confusion_matrix(
    conf_matrix: np.ndarray,
    class_names: List[str] = None,
    title: str = "Confusion Matrix",
    figsize: Tuple[float, float] = (6.4, 4.8),
    cmap: str = "Blues",
    normalize: bool = True,
    save_path: Optional[str] = None
) -> None:
    """
    绘制混淆矩阵图。
    
    参数:
        conf_matrix: 混淆矩阵数组
        class_names: 类别名称列表
        title: 图表标题
        figsize: 图表大小，默认为 matplotlib 的标准大小 (6.4, 4.8)
        cmap: 颜色映射
        normalize: 是否将混淆矩阵归一化为比例
        save_path: 保存路径，如果为None则不保存
    
    示例:
        conf_matrix = np.array([
            [120, 5, 2, 3],
            [8, 130, 4, 2],
            [5, 7, 125, 3],
            [6, 3, 8, 135]
        ])
        class_names = ['Method 1', 'Method 2', 'Method 3', 'Method 4']
        plot_confusion_matrix(conf_matrix, class_names)
    """
    # 创建图表
    plt.figure(figsize=figsize)
    
    # 如果没有提供类别名称，则使用索引
    if class_names is None:
        class_names = [str(i) for i in range(conf_matrix.shape[0])]
    
    # 归一化混淆矩阵
    if normalize:
        conf_matrix_norm = conf_matrix.astype('float') / conf_matrix.sum(axis=1)[:, np.newaxis]
        conf_matrix_display = conf_matrix_norm
        fmt = '.2f'
    else:
        conf_matrix_display = conf_matrix
        fmt = 'd'
    
    # 使用seaborn绘制热图
    ax = sns.heatmap(conf_matrix_display, annot=True, fmt=fmt, cmap=cmap,
                xticklabels=class_names, yticklabels=class_names, cbar=True)
    
    # 设置图表属性
    plt.title(title, fontsize=18, family='Times New Roman')
    plt.xlabel('Predicted Label', fontsize=16, family='Times New Roman')
    plt.ylabel('True Label', fontsize=16, family='Times New Roman')
    
    # 旋转x轴和y轴标签25度
    plt.xticks(fontsize=10, family='Times New Roman', rotation=25, ha='right')
    plt.yticks(fontsize=10, family='Times New Roman', rotation=25, va='top')
    
    # 设置图例框为直角黑框
    cbar = ax.collections[0].colorbar
    cbar.outline.set_edgecolor('black')
    cbar.outline.set_linewidth(1.0)
    
    # 保存图表
    if save_path:
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Chart saved to: {save_path}")
    
    # 显示图表
    plt.tight_layout()
    plt.show()


def main() -> None:
    """
    主函数，演示混淆矩阵绘制功能。
    """
    # 示例混淆矩阵数据
    # conf_matrix = np.array([[5158, 86, 73, 4, 51, 0, 0, 24], [8, 579, 0, 0, 0, 0, 0, 0], [2, 0, 598, 0, 0, 0, 0, 0], [8, 0, 0, 588, 4, 0, 0, 0], [8, 0, 2, 2, 587, 0, 0, 0], [0, 0, 0, 0, 0, 3, 0, 0], [8, 0, 0, 0, 1, 0, 2, 0], [6, 0, 40, 0, 12, 0, 0, 542]])
    
    # 类别名称
    # class_names = ['Normal', 'Bot', 'Brute', 'DDoS', 'DoS', 'Heartbleed', 'Infiltration', 'Web']



    # #Car
    conf_matrix = np.array([[2979, 1, 0, 17, 3], [0, 2854, 0, 0, 0], [37, 0, 1509, 9, 3], [631, 1, 36, 81, 1], [0, 0, 0, 0, 603]]) # 无DP
    class_names =['Normal', 'Flooding', 'Fuzzing', 'Replay', 'Spoofing'] 
    
    # 绘制归一化混淆矩阵
    plot_confusion_matrix(
        conf_matrix=conf_matrix,
        class_names=class_names,
        title="Confusion Matrix",
        figsize=(6.4, 4.8),
        normalize=True,
        save_path="normalized_confusion_matrix.png"
    )
    
    # # 绘制原始混淆矩阵
    # plot_confusion_matrix(
    #     conf_matrix=conf_matrix,
    #     class_names=class_names,
    #     title="Confusion Matrix",
    #     figsize=(6.4, 4.8),
    #     normalize=False,
    #     save_path="raw_confusion_matrix.png"
    # )


if __name__ == "__main__":
    main()
