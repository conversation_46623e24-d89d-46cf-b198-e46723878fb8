import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, classification_report

import copy
import os
import gc
import random
from tqdm import tqdm

import torch
from torch.utils.data import DataLoader, Dataset, TensorDataset
from torchvision import datasets, transforms

from src.data import *
from src.data.data import noniid_dirichlet
# Import models conditionally based on pruning mode - will be done after args parsing
from src.pruning import *
from src.sub_fedavg import *
from result.tsne_visualization import tsne_visualizer


def calculate_detailed_metrics(client, dataset_test, device, num_classes):
    """
    计算客户端的详细指标：precision, recall, F1, False alarm rate, confusion matrix

    Args:
        client: 客户端对象
        dataset_test: 测试数据集
        device: 计算设备
        num_classes: 类别数量

    Returns:
        dict: 包含所有指标的字典
    """
    # 客户端模型存储在 net 属性中
    client.net.eval()

    all_predictions = []
    all_targets = []

    # 获取所有预测和真实标签
    with torch.no_grad():
        for data, target in dataset_test:
            data, target = data.to(device), target.to(device)
            output = client.net(data)
            pred = output.argmax(dim=1)

            all_predictions.extend(pred.cpu().numpy())
            all_targets.extend(target.cpu().numpy())

    # 转换为numpy数组
    y_true = np.array(all_targets)
    y_pred = np.array(all_predictions)

    # 计算precision, recall, F1
    precision, recall, f1, support = precision_recall_fscore_support(
        y_true, y_pred, average=None, zero_division=0
    )

    # 计算宏平均和微平均
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='macro', zero_division=0
    )
    precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='micro', zero_division=0
    )

    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred, labels=list(range(num_classes)))

    # 计算False Alarm Rate (FPR) for each class
    false_alarm_rates = []
    for i in range(num_classes):
        # True Negatives: correctly predicted as not class i
        tn = np.sum(cm) - (np.sum(cm[i, :]) + np.sum(cm[:, i]) - cm[i, i])
        # False Positives: incorrectly predicted as class i
        fp = np.sum(cm[:, i]) - cm[i, i]

        # False Alarm Rate = FP / (FP + TN)
        if (fp + tn) > 0:
            far = fp / (fp + tn)
        else:
            far = 0.0
        false_alarm_rates.append(far)

    # 计算整体False Alarm Rate (平均)
    avg_false_alarm_rate = np.mean(false_alarm_rates)

    # 计算准确率
    accuracy = np.sum(y_pred == y_true) / len(y_true)

    return {
        'accuracy': accuracy,
        'precision_per_class': precision,
        'recall_per_class': recall,
        'f1_per_class': f1,
        'support_per_class': support,
        'precision_macro': precision_macro,
        'recall_macro': recall_macro,
        'f1_macro': f1_macro,
        'precision_micro': precision_micro,
        'recall_micro': recall_micro,
        'f1_micro': f1_micro,
        'false_alarm_rates': false_alarm_rates,
        'avg_false_alarm_rate': avg_false_alarm_rate,
        'confusion_matrix': cm,
        'y_true': y_true,
        'y_pred': y_pred
    }
from src.client import *
from src.utils.options_u import args_parser

def get_model_size_info(model_state_dict):
    """
    计算模型状态字典的大小信息

    Args:
        model_state_dict: 模型状态字典

    Returns:
        dict: 包含模型大小信息的字典
    """
    total_params = 0
    total_bytes = 0

    for key, param in model_state_dict.items():
        param_count = param.numel()
        param_bytes = param_count * param.element_size()

        total_params += param_count
        total_bytes += param_bytes

    return {
        'total_params': total_params,
        'total_bytes': total_bytes,
        'size_kb': total_bytes / 1024,
        'size_mb': total_bytes / (1024 ** 2),
        'size_gb': total_bytes / (1024 ** 3)
    }

def calculate_communication_cost(model_state_dict, participating_clients):
    """
    计算单轮通信消耗

    Args:
        model_state_dict: 模型状态字典
        participating_clients: 参与的客户端数量

    Returns:
        dict: 通信消耗信息
    """
    model_info = get_model_size_info(model_state_dict)

    # 上传：参与的客户端上传模型到服务器
    upload_mb = model_info['size_mb'] * participating_clients

    # 下载：服务器向参与的客户端下发聚合后的模型
    download_mb = model_info['size_mb'] * participating_clients

    total_mb = upload_mb + download_mb

    return {
        'model_size_mb': model_info['size_mb'],
        'model_params': model_info['total_params'],
        'participating_clients': participating_clients,
        'upload_mb': upload_mb,
        'download_mb': download_mb,
        'total_mb': total_mb
    }

def format_parameters(param_count):
    """
    格式化参数数量显示为K单位

    Args:
        param_count: 参数数量

    Returns:
        str: 格式化后的参数数量字符串
    """
    if param_count >= 1_000_000:
        return f"{param_count/1_000_000:.2f}M"
    elif param_count >= 1_000:
        return f"{param_count/1_000:.1f}K"
    else:
        return f"{param_count:,}"

def print_round_stats_model_updates(round_num, model_state_dict, participating_clients, total_clients, compression_ratio=1.0):
    """
    打印Model Updates模式的单轮统计信息

    Args:
        round_num: 轮次编号
        model_state_dict: 模型状态字典
        participating_clients: 参与的客户端数量
        total_clients: 总客户端数量
        compression_ratio: 压缩比例
    """
    model_info = get_model_size_info(model_state_dict)

    # 计算单个客户端的更新大小（正确的Update Size）
    single_update_size_mb = model_info['size_mb'] * compression_ratio

    # 计算总通信成本
    total_upload_cost = single_update_size_mb * participating_clients  # 所有客户端上传更新
    total_download_cost = model_info['size_mb'] * participating_clients  # 所有客户端下载完整模型
    total_communication = total_upload_cost + total_download_cost

    print(f"\n[STATS] Round {round_num} Statistics (Model Updates Mode):")
    print(f"   [UPDATE] Update Mode: Model Updates/Gradients")
    print(f"   [CHART] Model Parameters: {model_info['total_params']:,} ({format_parameters(model_info['total_params'])})")
    print(f"   [PACKAGE] Full Model Size: {model_info['size_mb']:.2f} MB")
    if compression_ratio < 1.0:
        print(f"   [SHUFFLE] Update Size (per client): {single_update_size_mb:.2f} MB ({compression_ratio*100:.1f}% of full model)")
    else:
        print(f"   [SHUFFLE] Update Size (per client): {single_update_size_mb:.2f} MB (no compression)")
    print(f"   [USERS] Participating Clients: {participating_clients}/{total_clients}")
    print(f"   [UPLOAD]  Upload Cost: {total_upload_cost:.2f} MB ({participating_clients} × {single_update_size_mb:.2f})")
    print(f"   [DOWNLOAD]  Download Cost: {total_download_cost:.2f} MB ({participating_clients} × {model_info['size_mb']:.2f})")
    print(f"   [STATS] Total Communication: {total_communication:.2f} MB")

def print_round_stats_full_params(round_num, model_state_dict, participating_clients, total_clients, compression_ratio=1.0):
    """
    打印Full Parameters模式的单轮统计信息

    Args:
        round_num: 轮次编号
        model_state_dict: 模型状态字典
        participating_clients: 参与的客户端数量
        total_clients: 总客户端数量
        compression_ratio: 压缩比例
    """
    model_info = get_model_size_info(model_state_dict)

    # 计算单个客户端的压缩参数大小
    single_compressed_size_mb = model_info['size_mb'] * compression_ratio

    # 计算总通信成本
    total_upload_cost = single_compressed_size_mb * participating_clients  # 所有客户端上传压缩参数
    total_download_cost = single_compressed_size_mb * participating_clients  # 所有客户端下载压缩参数
    total_communication = total_upload_cost + total_download_cost

    print(f"\n[STATS] Round {round_num} Statistics (Full Parameters Mode):")
    print(f"   [UPDATE] Update Mode: Full Parameters")
    print(f"   [CHART] Model Parameters: {model_info['total_params']:,} ({format_parameters(model_info['total_params'])})")
    print(f"   [PACKAGE] Full Model Size: {model_info['size_mb']:.2f} MB")
    if compression_ratio < 1.0:
        print(f"   [COMPRESS]  Compressed Size (per client): {single_compressed_size_mb:.2f} MB ({compression_ratio*100:.1f}% of full model)")
    else:
        print(f"   [PACKAGE] Parameter Size (per client): {single_compressed_size_mb:.2f} MB (no compression)")
    print(f"   [USERS] Participating Clients: {participating_clients}/{total_clients}")
    print(f"   [UPLOAD]  Upload Cost: {total_upload_cost:.2f} MB ({participating_clients} × {single_compressed_size_mb:.2f})")
    print(f"   [DOWNLOAD]  Download Cost: {total_download_cost:.2f} MB ({participating_clients} × {single_compressed_size_mb:.2f})")
    print(f"   [STATS] Total Communication: {total_communication:.2f} MB")

def print_round_stats(round_num, model_state_dict, participating_clients, total_clients):
    """
    通用的打印单轮统计信息函数（保持向后兼容）

    Args:
        round_num: 轮次编号
        model_state_dict: 模型状态字典
        participating_clients: 参与的客户端数量
        total_clients: 总客户端数量
    """
    model_info = get_model_size_info(model_state_dict)
    comm_info = calculate_communication_cost(model_state_dict, participating_clients)

    print(f"\n[STATS] Round {round_num} Statistics:")
    print(f"   Model Parameters: {model_info['total_params']:,} ({format_parameters(model_info['total_params'])})")
    print(f"   Model Size: {model_info['size_mb']:.2f} MB")
    print(f"   Participating Clients: {participating_clients}/{total_clients}")
    print(f"   Upload Cost: {comm_info['upload_mb']:.2f} MB")
    print(f"   Download Cost: {comm_info['download_mb']:.2f} MB")
    print(f"   Total Communication: {comm_info['total_mb']:.2f} MB")

def create_datasets_from_csv(csv_path: str, window_size: int = 1, train_ratio: float = 0.7, random_seed: int = 42):
    """
    从CSV文件创建训练和测试数据集。

    Args:
        csv_path: CSV文件路径
        window_size: 滑动窗口大小
        train_ratio: 训练集比例
        random_seed: 随机种子

    Returns:
        train_dataset, test_dataset: 训练和测试数据集
    """
    # Load CSV data
    df = pd.read_csv(csv_path)

    # 处理可能的无穷大或超出 float64 上限的数值
    # 1) 将正负无穷替换为 NaN
    df.replace([np.inf, -np.inf], np.nan, inplace=True)

    # 2) 将超过 float64 上限 (≈1.79e308) 的极端值裁剪为可表示的最大值
    float64_max: float = np.finfo(np.float64).max
    df = df.applymap(
        lambda x: float64_max if isinstance(x, (int, float, np.floating)) and x > float64_max else
        (-float64_max if isinstance(x, (int, float, np.floating)) and x < -float64_max else x)
    )

    # 3) 对仍存在的 NaN 行进行删除，避免后续 scaler 报错
    if df.isna().any().any():
        before_drop: int = len(df)
        df.dropna(inplace=True)
        print(f"已移除包含 NaN/Inf 的行: {before_drop - len(df)} 条 (剩余 {len(df)})")

    print(f"数据集形状: {df.shape}")

    # 分离特征和标签
    features = df.iloc[:, :-1].values.astype(np.float32)
    labels = df.iloc[:, -1].values.astype(np.int64)

    # Split data into train/test using sklearn's train_test_split
    X_train, X_test, y_train, y_test = train_test_split(
        features, labels,
        test_size=(1 - train_ratio),
        random_state=random_seed,
        stratify=labels  # Ensure balanced class distribution in both splits
    )

    print("============================data", X_train.shape, y_train.shape, X_test.shape, y_test.shape)
    # Normalize features using MinMaxScaler
    scaler = MinMaxScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)

    # Apply sliding window if needed
    if window_size > 1:
        X_train_windowed, y_train_windowed = _create_sliding_windows(X_train_scaled, y_train, window_size)
        X_test_windowed, y_test_windowed = _create_sliding_windows(X_test_scaled, y_test, window_size)
    else:
        # Add dimension for consistency: (N, f) -> (N, 1, f)
        X_train_windowed = X_train_scaled[:, np.newaxis, :]
        X_test_windowed = X_test_scaled[:, np.newaxis, :]
        y_train_windowed = y_train
        y_test_windowed = y_test

    # Create TensorDataset objects
    train_dataset = TensorDataset(
        torch.tensor(X_train_windowed, dtype=torch.float32),
        torch.tensor(y_train_windowed, dtype=torch.long)
    )

    test_dataset = TensorDataset(
        torch.tensor(X_test_windowed, dtype=torch.float32),
        torch.tensor(y_test_windowed, dtype=torch.long)
    )

    # Add targets attribute for compatibility with existing data splitting functions
    train_dataset.targets = y_train_windowed
    test_dataset.targets = y_test_windowed

    print(f"训练集大小: {len(train_dataset)}, 测试集大小: {len(test_dataset)}")

    return train_dataset, test_dataset


def _create_sliding_windows(features, labels, window_size):
    """
    创建滑动窗口数据。

    Args:
        features: 特征数组 (n_samples, n_features)
        labels: 标签数组 (n_samples,)
        window_size: 滑动窗口大小

    Returns:
        windowed_features: 窗口化特征 (n_windows, window_size, n_features)
        windowed_labels: 对应的标签 (n_windows,)
    """
    n_samples, n_features = features.shape
    n_windows = n_samples - window_size + 1

    windowed_features = np.zeros((n_windows, window_size, n_features))
    windowed_labels = np.zeros(n_windows, dtype=labels.dtype)

    for i in range(n_windows):
        windowed_features[i] = features[i:i + window_size]
        windowed_labels[i] = labels[i + window_size - 1]  # Use the last label in the window

    return windowed_features, windowed_labels

def set_random_seed(seed=42):
    """
    Set random seed for reproducibility across different libraries.

    Args:
        seed: Random seed value (default: 42)
    """
    # Python random module
    random.seed(seed)

    # NumPy random seed
    np.random.seed(seed)

    # PyTorch random seed
    torch.manual_seed(seed)

    # PyTorch CUDA random seed (for GPU reproducibility)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)  # For multi-GPU setups

        # Additional settings for deterministic behavior on GPU
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

args = args_parser()

# Import models based on pruning mode
if args.disable_pruning:
    print("=== IMPORTING STANDARD MODELS ===")
    from src.models.models import *
    print("Loaded models from src.models.models (standard implementation)")
else:
    print("=== IMPORTING PRUNING-ENABLED MODELS ===")
    from src.models.unstructured import *
    print("Loaded models from src.models.unstructured (supports pruning)")

# Set random seed for reproducibility
set_random_seed(args.seed)

args.device = torch.device('cuda:{}'.format(args.gpu) if torch.cuda.is_available() else 'cpu')

# Display device information
print(f"\n[DEVICE] DEVICE INFORMATION:")
print(f"   Device: {args.device}")
if args.device.type == 'cuda':
    print(f"   GPU ID: {args.gpu}")
    print(f"   GPU Name: {torch.cuda.get_device_name(args.gpu)}")
    print(f"   GPU Memory: {torch.cuda.get_device_properties(args.gpu).total_memory / 1024**3:.1f} GB")
    print(f"   CUDA Available: [OK] Yes")
else:
    print(f"   CUDA Available: [ERROR] No")
    print(f"   Using CPU for computation")

torch.cuda.set_device(args.gpu) ## Setting cuda on GPU



## Data partitioning section

if args.dataset == 'carh':
    csv_path = 'src/RawData/carh.csv'
    train_dataset, test_dataset = create_datasets_from_csv(csv_path, args.window_size, 0.7)

    nclass_carh = args.nclass
    nsamples_carh = args.nsample_pc

    if args.noniid:
        if args.shard:
            print(f'--CarH Non-IID-- {args.nclass} random Shards, Sample per shard {args.nsample_pc}')
            user_groups_train, user_groups_test = noniid_shard(args.dataset, train_dataset, test_dataset,
                            args.num_users, nclass_carh, nsamples_carh, args.split_test)

        elif args.label:
            print(f'--CarH Non-IID-- {args.nclass} random Label, Sample per label {args.nsample_pc}')
            user_groups_train, user_groups_test = \
            noniid_label(args.dataset, train_dataset, test_dataset, args.num_users, nclass_carh,
                                 nsamples_carh, args.split_test)

        elif args.dirichlet:
            alpha = args.alpha
            print(f'--CarH Non-IID-- Dirichlet distribution, alpha={alpha}')
            user_groups_train, user_groups_test = \
            noniid_dirichlet(args.dataset, train_dataset, test_dataset, args.num_users, alpha, args.split_test)

        else:
            exit('Error: unrecognized partitioning type')
    else:
        print(f'--CarH IID-- Split Test {args.split_test}')
        user_groups_train, user_groups_test = \
        iid(args.dataset, train_dataset, test_dataset, args.num_users, args.split_test)
            
elif args.dataset == 'cicids2017':
    csv_path = 'src/RawData/cicids2017.csv'
    train_dataset, test_dataset = create_datasets_from_csv(csv_path, args.window_size, 0.7)

    nclass_cicids2017 = args.nclass
    nsamples_cicids2017 = args.nsample_pc

    if args.noniid:
        if args.shard:
            print(f'--CICIDS2017 Non-IID-- {args.nclass} random Shards, Sample per shard {args.nsample_pc}')
            user_groups_train, user_groups_test = noniid_shard(args.dataset, train_dataset, test_dataset,
                        args.num_users, nclass_cicids2017, nsamples_cicids2017, args.split_test)

        elif args.label:
            print(f'--CICIDS2017 Non-IID-- {args.nclass} random Labels, Sample per label {args.nsample_pc}')
            user_groups_train, user_groups_test = \
            noniid_label(args.dataset, train_dataset, test_dataset, args.num_users, nclass_cicids2017,
                                 nsamples_cicids2017, args.split_test)

        elif args.dirichlet:
            alpha = args.alpha
            print(f'--CICIDS2017 Non-IID-- Dirichlet distribution, alpha={alpha}')
            user_groups_train, user_groups_test = \
            noniid_dirichlet(args.dataset, train_dataset, test_dataset, args.num_users, alpha, args.split_test)

        else:
            exit('Error: unrecognized partitioning type')
    else:
        print(f'--CICIDS2017 IID-- Split Test {args.split_test}')
        user_groups_train, user_groups_test = \
        iid(args.dataset, train_dataset, test_dataset, args.num_users, args.split_test)
            
elif args.dataset == 'toniot':
    csv_path = 'src/RawData/toniot.csv'
    train_dataset, test_dataset = create_datasets_from_csv(csv_path, args.window_size, 0.7)

    nclass_toniot = args.nclass
    nsamples_toniot = args.nsample_pc

    if args.noniid:
        if args.shard:
            print(f'--ToNIoT Non-IID-- {args.nclass} random Shards, Sample per shard {args.nsample_pc}')
            user_groups_train, user_groups_test = noniid_shard(args.dataset, train_dataset, test_dataset,
                            args.num_users, nclass_toniot, nsamples_toniot, args.split_test)
        elif args.label:
            print(f'--ToNIoT Non-IID-- {args.nclass} random Labels, Sample per label {args.nsample_pc}')
            user_groups_train, user_groups_test = \
            noniid_label(args.dataset, train_dataset, test_dataset, args.num_users, nclass_toniot,
                                 nsamples_toniot, args.split_test)

        elif args.dirichlet:
            alpha = args.alpha
            print(f'--ToNIoT Non-IID-- Dirichlet distribution, alpha={alpha}')
            user_groups_train, user_groups_test = \
            noniid_dirichlet(args.dataset, train_dataset, test_dataset, args.num_users, alpha, args.split_test)

        else:
            exit('Error: unrecognized partitioning type')
    else:
        print(f'--ToNIoT IID-- Split Test {args.split_test}')
        user_groups_train, user_groups_test = \
        iid(args.dataset, train_dataset, test_dataset, args.num_users, args.split_test)

else:
    exit(f'Error: Unsupported dataset {args.dataset}. Supported datasets: carh, cicids2017, toniot')

def print_client_data_info(user_groups_train, user_groups_test, train_dataset, test_dataset, num_users, dataset_name):
    """
    Print detailed information about data distribution across clients.

    Args:
        user_groups_train: Dictionary mapping client_id to train data indices
        user_groups_test: Dictionary mapping client_id to test data indices
        train_dataset: Training dataset
        test_dataset: Test dataset
        num_users: Number of clients
        dataset_name: Name of the dataset
    """
    print(f"\n{'='*80}")
    print(f"[STATS] CLIENT DATA DISTRIBUTION ANALYSIS - {dataset_name.upper()}")
    print(f"{'='*80}")

    # Extract targets
    train_targets = np.array(train_dataset.targets)
    test_targets = np.array(test_dataset.targets)

    # Get unique classes
    unique_classes = sorted(set(train_targets))
    num_classes = len(unique_classes)

    print(f"[CHART] Dataset Overview:")
    print(f"   Total Training Samples: {len(train_dataset):,}")
    print(f"   Total Test Samples: {len(test_dataset):,}")
    print(f"   Number of Classes: {num_classes}")
    print(f"   Classes: {unique_classes}")
    print(f"   Number of Clients: {num_users}")

    # Calculate overall class distribution
    train_class_counts = {cls: np.sum(train_targets == cls) for cls in unique_classes}
    test_class_counts = {cls: np.sum(test_targets == cls) for cls in unique_classes}

    print(f"\n[STATS] Overall Class Distribution:")
    print(f"   Training: {train_class_counts}")
    print(f"   Test: {test_class_counts}")

    # Analyze each client
    print(f"\n[USERS] Per-Client Data Analysis:")
    print(f"{'Client':<8} {'Train':<8} {'Test':<8} {'Train Classes':<20} {'Test Classes':<20} {'Train Distribution':<30} {'Test Distribution':<30}")
    print(f"{'-'*8} {'-'*8} {'-'*8} {'-'*20} {'-'*20} {'-'*30} {'-'*30}")

    total_train_samples = 0
    total_test_samples = 0

    for client_id in range(num_users):
        # Get client's data indices
        train_indices = user_groups_train[client_id]
        test_indices = user_groups_test[client_id]

        # Get client's labels
        client_train_labels = train_targets[train_indices]
        client_test_labels = test_targets[test_indices]

        # Count samples
        train_count = len(client_train_labels)
        test_count = len(client_test_labels)

        total_train_samples += train_count
        total_test_samples += test_count

        # Get unique classes for this client
        train_classes = sorted(set(client_train_labels))
        test_classes = sorted(set(client_test_labels))

        # Calculate class distribution for this client
        train_dist = {cls: np.sum(client_train_labels == cls) for cls in train_classes}
        test_dist = {cls: np.sum(client_test_labels == cls) for cls in test_classes}

        # Format distributions for display
        train_dist_str = ', '.join([f"{cls}:{count}" for cls, count in train_dist.items()])
        test_dist_str = ', '.join([f"{cls}:{count}" for cls, count in test_dist.items()])

        print(f"{client_id:<8} {train_count:<8} {test_count:<8} {str(train_classes):<20} {str(test_classes):<20} {train_dist_str:<30} {test_dist_str:<30}")

    print(f"{'-'*8} {'-'*8} {'-'*8} {'-'*20} {'-'*20} {'-'*30} {'-'*30}")
    print(f"{'Total':<8} {total_train_samples:<8} {total_test_samples:<8}")

    # Calculate statistics
    train_sizes = [len(user_groups_train[i]) for i in range(num_users)]
    test_sizes = [len(user_groups_test[i]) for i in range(num_users)]

    print(f"\n[CHART] Distribution Statistics:")
    print(f"   Training samples per client:")
    print(f"     Min: {min(train_sizes):,}, Max: {max(train_sizes):,}")
    print(f"     Mean: {np.mean(train_sizes):.1f}, Std: {np.std(train_sizes):.1f}")
    print(f"   Test samples per client:")
    print(f"     Min: {min(test_sizes):,}, Max: {max(test_sizes):,}")
    print(f"     Mean: {np.mean(test_sizes):.1f}, Std: {np.std(test_sizes):.1f}")

    # Calculate class diversity per client
    train_class_diversity = [len(set(train_targets[user_groups_train[i]])) for i in range(num_users)]
    test_class_diversity = [len(set(test_targets[user_groups_test[i]])) for i in range(num_users)]

    print(f"   Classes per client (training):")
    print(f"     Min: {min(train_class_diversity)}, Max: {max(train_class_diversity)}")
    print(f"     Mean: {np.mean(train_class_diversity):.1f}, Std: {np.std(train_class_diversity):.1f}")
    print(f"   Classes per client (test):")
    print(f"     Min: {min(test_class_diversity)}, Max: {max(test_class_diversity)}")
    print(f"     Mean: {np.mean(test_class_diversity):.1f}, Std: {np.std(test_class_diversity):.1f}")

    # Data balance analysis
    train_balance = np.std(train_sizes) / np.mean(train_sizes) if np.mean(train_sizes) > 0 else 0
    test_balance = np.std(test_sizes) / np.mean(test_sizes) if np.mean(test_sizes) > 0 else 0

    print(f"\n[BALANCE]  Data Balance Analysis:")
    print(f"   Training data balance (CV): {train_balance:.3f}")
    print(f"   Test data balance (CV): {test_balance:.3f}")
    print(f"   Balance interpretation: 0.0=perfectly balanced, >0.5=highly imbalanced")

    print(f"{'='*80}\n")

##
## Checking the partitions (total sample and labels for each client)

users_train_labels = {i: [] for i in range(args.num_users)}
users_test_labels = {i: [] for i in range(args.num_users)}

train_targets = np.array(train_dataset.targets)
test_targets = np.array(test_dataset.targets)

for i in range(args.num_users):
    ## Train Data for Each Client 
    train_count_per_client = 0 
    label = train_targets[user_groups_train[i]]
    train_count_per_client += len(label)
    label = set(label)
    users_train_labels[i] = list(label)
    
    # Test Data for Each Client 
    test_count_per_client = 0 
    label = test_targets[user_groups_test[i]]
    test_count_per_client += len(label)
    label = set(label)
    users_test_labels[i] = list(label) 
    
    #print(f'Client: {i}, Train Labels: {users_train_labels[i]}, Test Labels: {users_test_labels[i]},'
          #f' Num Train: {train_count_per_client}, Num Test: {test_count_per_client}')

# Print detailed client data distribution analysis
print_client_data_info(user_groups_train, user_groups_test, train_dataset, test_dataset, args.num_users, args.dataset)

##
# build model
print(f'MODEL: {args.model}, Dataset: {args.dataset}')

users_model = []

# Create intrusion detection models for CSV datasets
if args.model == 'intrusion_detection' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create light CNN + Transformer fusion model
    net_glob = create_intrusion_detection_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        d_model=getattr(args, 'light_d_model', 32),
        nhead=getattr(args, 'light_nhead', 1),
        dropout=args.model_dropout,
        hidden_dim=getattr(args, 'light_hidden_dim', 64),
        cnn_channels=args.cnn_channels
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_intrusion_detection_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            d_model=getattr(args, 'light_d_model', 32),
            nhead=getattr(args, 'light_nhead', 1),
            dropout=args.model_dropout,
            hidden_dim=getattr(args, 'light_hidden_dim', 64),
            cnn_channels=args.cnn_channels
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# GRU model for sequence-based intrusion detection
elif args.model == 'gru' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create GRU model using the convenience function
    net_glob = create_gru_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        dim_hidden=64
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_gru_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            dim_hidden=64
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# LSTM model for sequence-based intrusion detection
elif args.model == 'lstm' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create LSTM model using the convenience function
    net_glob = create_lstm_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        dim_hidden=128
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_lstm_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            dim_hidden=128
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# Transformer-only model for intrusion detection
elif args.model == 'transformer_only' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create Transformer-only model using the convenience function
    net_glob = create_transformer_only_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        transformer_layers=getattr(args, 'transformer_only_layers', args.transformer_layers),
        d_model=getattr(args, 'transformer_only_d_model', args.d_model),
        nhead=getattr(args, 'transformer_only_nhead', args.nhead),
        dropout=args.model_dropout,
        hidden_dim=getattr(args, 'transformer_only_hidden_dim', args.hidden_dim),
        projection_dim=getattr(args, 'transformer_only_projection_dim', args.d_model)
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_transformer_only_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            transformer_layers=getattr(args, 'transformer_only_layers', args.transformer_layers),
            d_model=getattr(args, 'transformer_only_d_model', args.d_model),
            nhead=getattr(args, 'transformer_only_nhead', args.nhead),
            dropout=args.model_dropout,
            hidden_dim=getattr(args, 'transformer_only_hidden_dim', args.hidden_dim),
            projection_dim=getattr(args, 'transformer_only_projection_dim', args.d_model)
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# CNN + Transformer parallel fusion model for intrusion detection
elif args.model == 'cnn_transformer' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create CNN + Transformer model using the convenience function
    net_glob = create_cnn_transformer_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        cnn_layers=getattr(args, 'cnn_transformer_cnn_layers', 2),
        transformer_layers=getattr(args, 'cnn_transformer_transformer_layers', 2),
        d_model=getattr(args, 'cnn_transformer_d_model', 128),
        nhead=getattr(args, 'cnn_transformer_nhead', 4),
        cnn_channels=getattr(args, 'cnn_transformer_cnn_channels', 64),
        dropout=args.model_dropout,
        hidden_dim=getattr(args, 'cnn_transformer_hidden_dim', 256),
        projection_dim=getattr(args, 'cnn_transformer_projection_dim', None)
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_cnn_transformer_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            cnn_layers=getattr(args, 'cnn_transformer_cnn_layers', 2),
            transformer_layers=getattr(args, 'cnn_transformer_transformer_layers', 2),
            d_model=getattr(args, 'cnn_transformer_d_model', 128),
            nhead=getattr(args, 'cnn_transformer_nhead', 4),
            cnn_channels=getattr(args, 'cnn_transformer_cnn_channels', 64),
            dropout=args.model_dropout,
            hidden_dim=getattr(args, 'cnn_transformer_hidden_dim', 256),
            projection_dim=getattr(args, 'cnn_transformer_projection_dim', None)
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# CNN-only model for intrusion detection
elif args.model == 'cnn_only' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create CNN-only model using the convenience function (EdgeCNN style)
    net_glob = create_cnn_only_model(
        dataset_name=args.dataset,
        input_dim=input_dim
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_cnn_only_model(
            dataset_name=args.dataset,
            input_dim=input_dim
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# EdgeCNN model for intrusion detection (edge computing optimized)
elif args.model == 'edge_cnn' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]

    # Create EdgeCNN model using the convenience function
    net_glob = create_edge_cnn_model(
        dataset_name=args.dataset,
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_edge_cnn_model(
            dataset_name=args.dataset,
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# Keep original models for backward compatibility
elif args.model == 'lenet5' and args.dataset == 'cifar10':
    net_glob = LeNet5Cifar10().to(args.device)
    net_glob.apply(weight_init)
    users_model = [LeNet5Cifar10().to(args.device).apply(weight_init) for _ in range(args.num_users)]
elif args.model == 'lenet5' and args.dataset == 'cifar100':
    net_glob = LeNet5Cifar100().to(args.device)
    net_glob.apply(weight_init)
    users_model = [LeNet5Cifar100().to(args.device).apply(weight_init) for _ in range(args.num_users)]
elif args.model == 'lenet5' and args.dataset == 'mnist':
    net_glob = LeNet5Mnist().to(args.device)
    net_glob.apply(weight_init)
    users_model = [LeNet5Mnist().to(args.device).apply(weight_init) for _ in range(args.num_users)]
else:
    exit(f'Error: Unsupported model-dataset combination: {args.model}-{args.dataset}')

if args.load_initial:
    initial_state_dict = torch.load(args.load_initial)
    net_glob.load_state_dict(initial_state_dict)

initial_state_dict = copy.deepcopy(net_glob.state_dict())
server_state_dict = copy.deepcopy(net_glob.state_dict())

for i in range(args.num_users):
    users_model[i].load_state_dict(initial_state_dict)

def safe_model_copy(original_model, device):
    # """
    # Safely create a copy of a PyTorch model to avoid deepcopy issues.

    # Uses clone().detach() method for efficient tensor copying without serialization overhead.
    # Automatically detects model type and extracts necessary constructor parameters.

    # Args:
    #     original_model: The original PyTorch model to copy
    #     device: The device to place the copied model on

    # Returns:
    #     A new model instance with the same architecture and state_dict as the original
    # """
    # # 方式A：深拷贝 state_dict（并 clone tensor 防止共享）
    # # Clone and detach all tensors in state_dict to ensure complete independence
    sd = {k: v.clone().detach() for k, v in original_model.state_dict().items()}

    # Get model class name to determine constructor parameters
    model_class_name = type(original_model).__name__

    # Create new model instance with proper parameters based on model type
    if 'IntrusionDetection' in model_class_name:
        # Extract parameters for light CNN + Transformer fusion model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        d_model = getattr(original_model, 'd_model', 32)
        nhead = getattr(original_model, 'nhead', 1)
        dropout = getattr(original_model, 'dropout', 0.3)
        hidden_dim = getattr(original_model, 'hidden_dim', 64)
        cnn_channels = getattr(original_model, 'cnn_channels', 32)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        # from src.models.models import create_intrusion_detection_model
        new_model = create_intrusion_detection_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            d_model=d_model,
            nhead=nhead,
            dropout=dropout,
            hidden_dim=hidden_dim,
            cnn_channels=cnn_channels  # 从原始模型中提取
        )

    elif 'GRU' in model_class_name:
        # Extract parameters for GRU model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        dim_hidden = getattr(original_model, 'dim_hidden', 64)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        # from src.models.models import create_gru_model
        new_model = create_gru_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            dim_hidden=dim_hidden
        )

    elif 'LSTM' in model_class_name:
        # Extract parameters for LSTM model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        dim_hidden = getattr(original_model, 'dim_hidden', 128)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        # from src.models.models import create_lstm_model
        new_model = create_lstm_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            dim_hidden=dim_hidden
        )

    elif 'LeNet5' in model_class_name:
        # LeNet5 models don't need parameters
        if 'Cifar10' in model_class_name:
            # from src.models.models import LeNet5Cifar10
            new_model = LeNet5Cifar10()
        elif 'Cifar100' in model_class_name:
            # from src.models.models import LeNet5Cifar100
            new_model = LeNet5Cifar100()
        elif 'Mnist' in model_class_name:
            # from src.models.models import LeNet5Mnist
            new_model = LeNet5Mnist()
        else:
            raise ValueError(f"Unknown LeNet5 variant: {model_class_name}")

    elif 'TransformerOnly' in model_class_name:
        # Extract parameters for TransformerOnly model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        transformer_layers = getattr(original_model, 'transformer_layers', 2)  # [OK] FIXED: Match create_transformer_only_model
        d_model = getattr(original_model, 'd_model', 64)  # [OK] FIXED: Match create_transformer_only_model
        nhead = getattr(original_model, 'nhead', 4)  # [OK] FIXED: Match create_transformer_only_model
        dropout = getattr(original_model, 'dropout', 0.3)  # [OK] FIXED: Match create_transformer_only_model
        hidden_dim = getattr(original_model, 'hidden_dim', 128)  # [OK] FIXED: Match create_transformer_only_model

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        # from src.models.models import create_transformer_only_model
        projection_dim = getattr(original_model, 'projection_dim', args.d_model)
        new_model = create_transformer_only_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            transformer_layers=transformer_layers,
            d_model=d_model,
            nhead=nhead,
            dropout=dropout,
            hidden_dim=hidden_dim,
            projection_dim=projection_dim
        )

    elif 'CNNTransformer' in model_class_name:
        # Extract parameters for CNNTransformer model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        cnn_layers = getattr(original_model, 'cnn_layers', 2)
        transformer_layers = getattr(original_model, 'transformer_layers', 2)
        d_model = getattr(original_model, 'd_model', 128)
        nhead = getattr(original_model, 'nhead', 4)
        cnn_channels = getattr(original_model, 'cnn_channels', 64)
        dropout = getattr(original_model, 'dropout', 0.1)
        hidden_dim = getattr(original_model, 'hidden_dim', 256)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        # from src.models.models import create_cnn_transformer_model
        projection_dim = getattr(original_model, 'projection_dim', None)
        new_model = create_cnn_transformer_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            cnn_layers=cnn_layers,
            transformer_layers=transformer_layers,
            d_model=d_model,
            nhead=nhead,
            cnn_channels=cnn_channels,
            dropout=dropout,
            hidden_dim=hidden_dim,
            projection_dim=projection_dim
        )

    elif 'CNNOnly' in model_class_name:
        # Extract parameters for CNNOnly model (EdgeCNN style)
        input_dim = getattr(original_model, 'input_dim', 78)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        # from src.models.models import create_cnn_only_model
        new_model = create_cnn_only_model(
            dataset_name=dataset_name,
            input_dim=input_dim
        )

    elif model_class_name == 'EdgeCNN':
        # Extract parameters for EdgeCNN model
        n_classes = getattr(original_model, 'dense')[-1].out_features  # Get number of classes from last layer

        # Create new EdgeCNN model with correct parameters
        # from src.models.models import EdgeCNN
        new_model = EdgeCNN(
            n_classes=n_classes
        )

    else:
        # For unknown model types, try direct instantiation as fallback
        try:
            new_model = type(original_model)()
        except Exception as e:
            raise ValueError(f"Cannot create model copy for unknown type {model_class_name}: {e}")

    # Move to target device first
    new_model = new_model.to(device)

    # Load the cloned state_dict (TokenLearner layers should exist now)
    new_model.load_state_dict(sd)

    return new_model

## Create clients based on pruning mode
clients = []

if not args.disable_pruning:
    print("=== PRUNING MODE ENABLED ===")
    print("Using Client_Sub_Un with pruning functionality")

    # Initialize pruning masks
    mask_init = make_init_mask(net_glob)

    for idx in range(args.num_users):
        # Use safe model copy to avoid deepcopy issues
        user_model_copy = safe_model_copy(users_model[idx], args.device)

        clients.append(Client_Sub_Un(idx, user_model_copy, args.local_bs, args.local_ep,
                   args.lr, args.device, copy.deepcopy(mask_init),
                   args.pruning_target, train_dataset, user_groups_train[idx],
                   test_dataset, user_groups_test[idx],
                   beta1=args.beta1, beta2=args.beta2, eps=args.eps, weight_decay=args.weight_decay))
else:
    print("=== STANDARD FEDAVG MODE ENABLED ===")
    print("Using Client_Standard without pruning functionality")

    for idx in range(args.num_users):
        # Use safe model copy to avoid deepcopy issues with intrusion_detection model
        user_model_copy = safe_model_copy(users_model[idx], args.device)

        # Test: Use copy.deepcopy to see if it causes NaN issues
        clients.append(Client_Standard(idx, user_model_copy , args.local_bs, args.local_ep,  #copy.deepcopy(users_model[idx])
                   args.lr, args.device, train_dataset, user_groups_train[idx],
                   test_dataset, user_groups_test[idx],
                   beta1=args.beta1, beta2=args.beta2, eps=args.eps, weight_decay=args.weight_decay,
                   topk_ratio=args.topk_ratio, enable_error_feedback=args.enable_error_feedback,
                   use_model_updates=args.use_model_updates))
    
## 
loss_train = []

init_tracc_pr = []  # initial train accuracy for each round 
final_tracc_pr = [] # final train accuracy for each round 

init_tacc_pr = []  # initial test accuarcy for each round 
final_tacc_pr = [] # final test accuracy for each round

init_tloss_pr = []  # initial test loss for each round
final_tloss_pr = [] # final test loss for each round

# Lists to store global model evaluation results for each round
global_test_loss_per_round = []  # global model test loss for each round
global_test_acc_per_round = []   # global model test accuracy for each round

clients_best_acc = [0 for _ in range(args.num_users)]
w_locals, loss_locals = [], []

# Initialize tracking variables based on mode
if not args.disable_pruning:
    masks = []
    ckp_avg_pruning = []
    ckp_avg_best_tacc_before = []
    ckp_avg_best_tacc_after = []

init_local_tacc = []       # initial local test accuracy at each round
final_local_tacc = []  # final local test accuracy at each round

init_local_tloss = []      # initial local test loss at each round
final_local_tloss = []     # final local test loss at each round

ckp_avg_tacc = []

# Lists to store all clients' average metrics for each round
all_clients_train_loss_per_round = []  # Average train loss of all clients for each round
all_clients_test_loss_per_round = []   # Average test loss of all clients for each round
all_clients_train_acc_per_round = []   # Average train accuracy of all clients for each round
all_clients_test_acc_per_round = []    # Average test accuracy of all clients for each round

# Communication tracking variables
communication_logs = []  # Store communication cost for each round
total_communication_mb = 0.0  # Total communication cost across all rounds

# Print initial model analysis
print(f"\n{'='*80}")
print(f"[BUILD]  MODEL ANALYSIS - {args.model.upper()} on {args.dataset.upper()}")
print(f"{'='*80}")

initial_model_info = get_model_size_info(net_glob.state_dict())
print(f"[STATS] Model Information:")
print(f"   Total Parameters: {initial_model_info['total_params']:,} ({format_parameters(initial_model_info['total_params'])})")
print(f"   Model Size: {initial_model_info['size_mb']:.2f} MB")
print(f"   Model Size: {initial_model_info['total_bytes']:,} bytes")

print(f"\n[COMM] Communication Analysis:")
print(f"   Model transmission size: {initial_model_info['size_mb']:.2f} MB per client")
print(f"   Expected clients per round: {max(int(args.frac * args.num_users), 1)}")
expected_per_round = initial_model_info['size_mb'] * max(int(args.frac * args.num_users), 1) * 2  # upload + download
print(f"   Expected communication per round: {expected_per_round:.2f} MB")
print(f"   Expected total communication ({args.rounds} rounds): {expected_per_round * args.rounds:.2f} MB")
print(f"{'='*80}")

# Create tqdm progress bar for training rounds
progress_bar = tqdm(range(args.rounds), desc="[ROCKET] Training Progress", unit="round")

for iteration in progress_bar:
        
    m = max(int(args.frac * args.num_users), 1)
    idxs_users = np.random.choice(range(args.num_users), m, replace=False)
    
    if args.is_print:
        print(f'###### ROUND {iteration+1} ######')
        print(f'Clients {idxs_users}')
    
    for idx in idxs_users:

        # Update client model with global model (different for each mode)
        if iteration+1 > 1:
            if not args.disable_pruning:
                # Pruning mode: use Sub_FedAvg_U_initial
                dic = Sub_FedAvg_U_initial(copy.deepcopy(clients[idx].get_mask()),
                                         copy.deepcopy(clients[idx].get_net()), server_state_dict)
                clients[idx].set_state_dict(dic)
            else:
                # Standard mode: directly set state dict
                clients[idx].set_state_dict(copy.deepcopy(server_state_dict))

        # Save initial parameters for model updates mode
        if args.use_model_updates and args.disable_pruning:  # Only for standard mode
            clients[idx].save_initial_params()

        # Evaluate before training
        loss, acc = clients[idx].eval_test()
        init_local_tacc.append(acc)
        init_local_tloss.append(loss)

        # Train client (different parameters for each mode)
        if not args.disable_pruning:
            # Pruning mode: pass pruning parameters
            loss = clients[idx].train(args.pruning_percent, args.dist_thresh, args.acc_thresh, is_print=False)
            # Collect masks for pruning mode
            masks.append(copy.deepcopy(clients[idx].get_mask()))
        else:
            # Standard mode: simple training
            loss = clients[idx].train(is_print=False)
            # Update client statistics
            clients[idx].increment_count()

        # Collect model weights and losses with Top-K compression
        if args.topk_ratio < 1.0:
            # Use Top-K compressed parameters
            compressed_params, indices_dict = clients[idx].get_compressed_params()
            w_locals.append({
                'params': copy.deepcopy(compressed_params),
                'indices': copy.deepcopy(indices_dict),
                'compressed': True
            })
        else:
            # Use original data (no compression)
            if args.use_model_updates:
                # Use model updates without compression
                updates_dict = clients[idx].get_model_updates()
                w_locals.append({
                    'params': copy.deepcopy(updates_dict),
                    'indices': {},
                    'compressed': False
                })
            else:
                # Use full parameters without compression
                w_locals.append({
                    'params': copy.deepcopy(clients[idx].get_state_dict()),
                    'indices': {},
                    'compressed': False
                })
        loss_locals.append(copy.deepcopy(loss))

        # Evaluate after training
        loss, acc = clients[idx].eval_test()

        # Update best accuracy
        if acc > clients_best_acc[idx]:
            clients_best_acc[idx] = acc

        # Update client's best accuracy for standard mode
        if args.disable_pruning:
            clients[idx].update_best_acc(acc)
  
        final_local_tacc.append(acc)
        final_local_tloss.append(loss)           
        
    # Aggregate models based on mode
    if not args.disable_pruning:
        # Debug: Print parameter names to identify mismatch
        print(f"\n[SEARCH] DEBUG: Server model parameters:")
        for name in list(server_state_dict.keys())[:5]:  # Print first 5 parameters
            print(f"   Server: {name}")

        print(f"\n[SEARCH] DEBUG: Client model parameters:")
        if w_locals:
            client_params = w_locals[0]['params'] if isinstance(w_locals[0], dict) else w_locals[0]
            for name in list(client_params.keys())[:5]:  # Print first 5 parameters
                print(f"   Client: {name}")

        print(f"\n[SEARCH] DEBUG: Total server params: {len(server_state_dict)}")
        print(f"[SEARCH] DEBUG: Total client params: {len(client_params) if w_locals else 0}")

        # Extract parameters from w_locals for Sub_FedAVG_U
        w_locals_params = []
        for client_data in w_locals:
            if isinstance(client_data, dict) and 'params' in client_data:
                w_locals_params.append(client_data['params'])
            else:
                w_locals_params.append(client_data)

        # Pruning mode: use Sub_FedAVG_U with masks
        server_state_dict = Sub_FedAVG_U(server_state_dict, w_locals_params, masks)
    else:
        # Standard mode: use FedAvg with proper parameters
        # Option 1: Use full FedAvg_standard with global model and sample counts
        # Calculate sample counts for weighted averaging
        sample_counts = [len(user_groups_train[idx]) for idx in idxs_users]
        server_state_dict = FedAvg_standard(server_state_dict, w_locals, sample_counts, args.use_model_updates, args.topk_ratio)

    # Evaluate global model on test set after aggregation
    net_glob.load_state_dict(server_state_dict)
    net_glob.eval()

    # Create test data loader for global evaluation
    test_loader_global = DataLoader(test_dataset, batch_size=args.local_bs, shuffle=False)

    global_test_loss = 0
    global_correct = 0
    global_total = 0

    with torch.no_grad():
        for data, target in test_loader_global:
            data, target = data.to(args.device), target.to(args.device)
            output = net_glob(data)

            # Calculate loss
            global_test_loss += F.cross_entropy(output, target, reduction='sum').item()

            # Get predictions
            pred = output.argmax(dim=1, keepdim=True)
            global_correct += pred.eq(target.view_as(pred)).sum().item()
            global_total += target.size(0)

    # Calculate final metrics for this round
    global_test_loss /= global_total
    global_test_accuracy = 100. * global_correct / global_total

    # Store global model results
    global_test_loss_per_round.append(global_test_loss)
    global_test_acc_per_round.append(global_test_accuracy)

    # # Perform feature extraction and t-SNE visualization at round 50 for CNNTransformerModel
    # if iteration + 1 == 50:
    #     # iteration = 49
    #     print(f"\n{'='*60}")
    #     print(f"[ART] 检查模型类型并进行特征提取（第{iteration + 1}轮）")
    #     print(f"{'='*60}")

    #     # 检查模型是否为 CNNTransformerModel
    #     model_class_name = net_glob.__class__.__name__
    #     print(f"当前模型类型: {model_class_name}")

    #     if model_class_name == 'CNNTransformerModel':
    #         try:
    #             print("[OK] 检测到CNNTransformerModel，开始提取特征...")

    #             # 定义特征和标签的存储文件路径
    #             features_file = f"features_round{iteration + 1}_{args.dataset}.npy"
    #             labels_file = f"labels_round{iteration + 1}_{args.dataset}.npy"

    #             # 检查是否已存在特征文件
    #             if os.path.exists(features_file) and os.path.exists(labels_file):
    #                 print(f"[FOLDER] 发现已存储的特征文件，直接读取...")
    #                 print(f"   特征文件: {features_file}")
    #                 print(f"   标签文件: {labels_file}")

    #                 # 从文件读取特征和标签
    #                 features = np.load(features_file)
    #                 labels = np.load(labels_file)
    #                 print(f"[OK] 成功读取特征: {features.shape}, 标签: {labels.shape}")

    #             else:
    #                 print(f"[FOLDER] 未发现特征文件，开始提取...")

    #                 # 提取特征和标签
    #                 features, labels = tsne_visualizer.extract_features_from_model(
    #                     model=net_glob,
    #                     data_loader=test_loader_global,
    #                     device=args.device
    #                 )

    #                 if features is not None and labels is not None:
    #                     print(f"[OK] 成功提取特征: {features.shape}, 标签: {labels.shape}")

    #                     # 保存特征和标签到文件
    #                     np.save(features_file, features)
    #                     np.save(labels_file, labels)
    #                     print(f"[SAVE] 特征已保存到: {features_file}")
    #                     print(f"[SAVE] 标签已保存到: {labels_file}")

    #                 else:
    #                     print("[ERROR] 特征提取失败")
    #                     features, labels = None, None

    #             # 如果成功获取特征和标签，执行 t-SNE 可视化
    #             if features is not None and labels is not None:
    #                 # 准备测试指标字典
    #                 test_metric = {
    #                     'features_before_classifier': features,
    #                     'y_true': labels
    #                 }

    #                 # 执行 t-SNE 可视化
    #                 tsne_visualizer._perform_tsne_visualization(test_metric, args.dataset)

    #             else:
    #                 print("[ERROR] 无法获取特征和标签，跳过t-SNE可视化")

    #         except Exception as e:
    #             print(f"[ERROR] 特征提取和t-SNE可视化过程中出错: {e}")
    #             import traceback
    #             traceback.print_exc()
    #     else:
    #         print(f"[INFO]  当前模型({model_class_name})不是CNNTransformerModel，跳过特征提取")

    # Calculate and record communication costs for this round
    participating_clients = len(idxs_users)
    round_comm_cost = calculate_communication_cost(server_state_dict, participating_clients)
    communication_logs.append({
        'round': iteration + 1,
        'model_params': round_comm_cost['model_params'],
        'model_params_formatted': format_parameters(round_comm_cost['model_params']),
        'model_size_mb': round_comm_cost['model_size_mb'],
        'participating_clients': participating_clients,
        'upload_mb': round_comm_cost['upload_mb'],
        'download_mb': round_comm_cost['download_mb'],
        'total_mb': round_comm_cost['total_mb']
    })
    total_communication_mb += round_comm_cost['total_mb']

    # Print round statistics if requested
    if args.print_model_stats:
        # Choose appropriate statistics function based on update mode
        if args.use_model_updates:
            print_round_stats_model_updates(
                iteration + 1,
                server_state_dict,
                participating_clients,
                args.num_users,
                compression_ratio=args.topk_ratio
            )
        else:
            print_round_stats_full_params(
                iteration + 1,
                server_state_dict,
                participating_clients,
                args.num_users,
                compression_ratio=args.topk_ratio
            )

    # print loss
    loss_avg = sum(loss_locals) / len(loss_locals)
    avg_init_tloss = sum(init_local_tloss) / len(init_local_tloss)
    avg_init_tacc = sum(init_local_tacc) / len(init_local_tacc)
    avg_final_tloss = sum(final_local_tloss) / len(final_local_tloss)
    avg_final_tacc = sum(final_local_tacc) / len(final_local_tacc)
    
    if args.is_print:    
        print('## END OF ROUND ##')
        template = "Round {}, Average Train loss {:.3f}"
        print(template.format(iteration+1, loss_avg))

        template = "AVG Init Test Loss: {:.3f}, AVG Init Test Acc: {:.3f}"
        print(template.format(avg_init_tloss, avg_init_tacc))

        template = "AVG Final Test Loss: {:.3f}, AVG Final Test Acc: {:.3f}"
        print(template.format(avg_final_tloss, avg_final_tacc))

        template = "Global Model Test Loss: {:.3f}, Global Model Test Acc: {:.3f}"
        print(template.format(global_test_loss, global_test_accuracy))

        # Print Top-K compression and model update mode statistics if enabled
        if iteration == 0:  # Print only in first round
            if args.use_model_updates:
                print(f"\n[UPDATE] Model Updates Mode Enabled:")
                print(f"   Communication Mode: Model Updates/Gradients")
                print(f"   Update Aggregation: Weighted sum applied to global model")

            if args.topk_ratio < 1.0:
                print(f"\n[TOOL] Top-K Compression Enabled:")
                print(f"   Compression Ratio: {args.topk_ratio:.2f} ({args.topk_ratio*100:.1f}% of parameters)")
                print(f"   Error Feedback: {'Enabled' if args.enable_error_feedback else 'Disabled'}")
                print(f"   Applied to: {'Model Updates' if args.use_model_updates else 'Full Parameters'}")

                # Get compression stats from first client
                if len(idxs_users) > 0:
                    stats = clients[idxs_users[0]].get_compression_stats()
                    if stats:
                        print(f"   Total Parameters: {stats['total_parameters']:,}")
                        print(f"   Compressed Parameters: {stats['compressed_parameters']:,}")
                        print(f"   Bandwidth Savings: {stats['bandwidth_savings']}")
                print()

    if (iteration + 1)%args.print_freq == 0:
        print('--- PRINTING ALL CLIENTS STATUS ---')
        current_acc = []
        best_acc_list = []

        if not args.disable_pruning:
            # Pruning mode: collect pruning statistics
            pruning_state = []
            best_acc_before_pruning = []

            for k in range(args.num_users):
                best_acc_before_pruning.append(clients[k].get_best_acc())
                pruning_state.append(clients[k].get_pruning())
                loss, acc = clients[k].eval_test()
                current_acc.append(acc)
                best_acc_list.append(clients[k].get_best_acc())

                template = ("Client {:3d}, labels {}, count {}, pruning_state {:3.3f}, "
                           "best_acc_before_pruning {:3.3f}, after_pruning {:3.3f}, current_acc {:3.3f} \n")

                print(template.format(k, users_train_labels[k], clients[k].get_count(), pruning_state[-1],
                                     best_acc_before_pruning[-1], clients_best_acc[k], current_acc[-1]))

            template = ("Round {:1d}, Avg Pruning {:3.3f}, Avg current_acc {:3.3f}, "
                         "Avg best_acc_before_pruning {:3.3f}, after_pruning {:3.3f}")

            print(template.format(iteration+1, np.mean(pruning_state), np.mean(current_acc),
                                  np.mean(best_acc_before_pruning), np.mean(clients_best_acc)))

            # Store pruning statistics
            ckp_avg_pruning.append(np.mean(pruning_state))
            ckp_avg_best_tacc_before.append(np.mean(best_acc_before_pruning))
            ckp_avg_best_tacc_after.append(np.mean(clients_best_acc))

            # 找到具有最大 current_acc 的客户端并计算详细指标 (Pruning Mode)
            if current_acc:  # 确保有数据
                best_client_idx = np.argmax(current_acc)
                best_client_acc = current_acc[best_client_idx]

                print(f"\n[TROPHY] Best Client Analysis (Round {iteration+1}, Pruning Mode):")
                print(f"   Client ID: {best_client_idx}")
                print(f"   Current Accuracy: {best_client_acc:.4f}")
                print(f"   Pruning State: {pruning_state[best_client_idx]:.4f}")

                # 计算详细指标
                try:
                    # 获取数据集信息
                    if args.dataset == 'cicids2017':
                        num_classes = 8
                    elif args.dataset == 'carh':
                        num_classes = 5
                    elif args.dataset == 'toniot':
                        num_classes = 10
                    else:
                        num_classes = 8  # 默认值

                    # 获取客户端的测试数据
                    client_test_data = clients[best_client_idx].ldr_test

                    # 计算详细指标
                    detailed_metrics = calculate_detailed_metrics(
                        clients[best_client_idx],
                        client_test_data,
                        args.device,
                        num_classes
                    )

                    # 打印详细指标
                    print(f"\n[STATS] Detailed Metrics for Best Client {best_client_idx} (Pruning Mode):")
                    print(f"   Accuracy: {detailed_metrics['accuracy']:.4f}")
                    print(f"   Precision (Macro): {detailed_metrics['precision_macro']:.4f}")
                    print(f"   Recall (Macro): {detailed_metrics['recall_macro']:.4f}")
                    print(f"   F1-Score (Macro): {detailed_metrics['f1_macro']:.4f}")
                    print(f"   False Alarm Rate (Avg): {detailed_metrics['avg_false_alarm_rate']:.4f}")

                    print(f"\n[CHART] Per-Class Metrics:")
                    for i in range(num_classes):
                        if i < len(detailed_metrics['precision_per_class']):
                            print(f"   Class {i}: Precision={detailed_metrics['precision_per_class'][i]:.4f}, "
                                  f"Recall={detailed_metrics['recall_per_class'][i]:.4f}, "
                                  f"F1={detailed_metrics['f1_per_class'][i]:.4f}, "
                                  f"FAR={detailed_metrics['false_alarm_rates'][i]:.4f}")

                    print(f"\n[NUMBERS] Confusion Matrix:")
                    print(detailed_metrics['confusion_matrix'])

                    # 存储详细指标到全局变量
                    if not hasattr(calculate_detailed_metrics, 'best_client_metrics_pruning'):
                        calculate_detailed_metrics.best_client_metrics_pruning = []

                    calculate_detailed_metrics.best_client_metrics_pruning.append({
                        'round': iteration + 1,
                        'client_id': best_client_idx,
                        'pruning_state': pruning_state[best_client_idx],
                        'metrics': detailed_metrics
                    })

                except Exception as e:
                    print(f"[ERROR] Error calculating detailed metrics: {e}")
                    import traceback
                    traceback.print_exc()

        else:
            # Standard mode: simpler statistics
            for k in range(args.num_users):
                loss, acc = clients[k].eval_test()
                current_acc.append(acc)
                best_acc_list.append(clients[k].get_best_acc())

                template = ("Client {:3d}, labels {}, count {}, "
                           "best_acc {:3.3f}, current_acc {:3.3f} \n")

                print(template.format(k, users_train_labels[k], clients[k].get_count(),
                                     clients[k].get_best_acc(), current_acc[-1]))

            template = ("Round {:1d}, Avg current_acc {:3.3f}, Avg best_acc {:3.3f}")

            print(template.format(iteration+1, np.mean(current_acc), np.mean(best_acc_list)))

            # 找到具有最大 current_acc 的客户端并计算详细指标
            if current_acc:  # 确保有数据
                best_client_idx = np.argmax(current_acc)
                best_client_acc = current_acc[best_client_idx]

                print(f"\n[TROPHY] Best Client Analysis (Round {iteration+1}):")
                print(f"   Client ID: {best_client_idx}")
                print(f"   Current Accuracy: {best_client_acc:.4f}")

                # 计算详细指标
                try:
                    # 获取数据集信息
                    if args.dataset == 'cicids2017':
                        num_classes = 8
                    elif args.dataset == 'carh':
                        num_classes = 5
                    elif args.dataset == 'toniot':
                        num_classes = 10
                    else:
                        num_classes = 8  # 默认值

                    # 获取客户端的测试数据
                    client_test_data = clients[best_client_idx].ldr_test

                    # 计算详细指标
                    detailed_metrics = calculate_detailed_metrics(
                        clients[best_client_idx],
                        client_test_data,
                        args.device,
                        num_classes
                    )

                    # 打印详细指标
                    print(f"\n[STATS] Detailed Metrics for Best Client {best_client_idx}:")
                    print(f"   Accuracy: {detailed_metrics['accuracy']:.4f}")
                    print(f"   Precision (Macro): {detailed_metrics['precision_macro']:.4f}")
                    print(f"   Recall (Macro): {detailed_metrics['recall_macro']:.4f}")
                    print(f"   F1-Score (Macro): {detailed_metrics['f1_macro']:.4f}")
                    print(f"   False Alarm Rate (Avg): {detailed_metrics['avg_false_alarm_rate']:.4f}")

                    print(f"\n[CHART] Per-Class Metrics:")
                    for i in range(num_classes):
                        if i < len(detailed_metrics['precision_per_class']):
                            print(f"   Class {i}: Precision={detailed_metrics['precision_per_class'][i]:.4f}, "
                                  f"Recall={detailed_metrics['recall_per_class'][i]:.4f}, "
                                  f"F1={detailed_metrics['f1_per_class'][i]:.4f}, "
                                  f"FAR={detailed_metrics['false_alarm_rates'][i]:.4f}")

                    print(f"\n[NUMBERS] Confusion Matrix:")
                    print(detailed_metrics['confusion_matrix'])

                    # 存储详细指标到全局变量或文件
                    if not hasattr(calculate_detailed_metrics, 'best_client_metrics'):
                        calculate_detailed_metrics.best_client_metrics = []

                    calculate_detailed_metrics.best_client_metrics.append({
                        'round': iteration + 1,
                        'client_id': best_client_idx,
                        'metrics': detailed_metrics
                    })

                except Exception as e:
                    print(f"[ERROR] Error calculating detailed metrics: {e}")
                    import traceback
                    traceback.print_exc()

        # Store common statistics
        ckp_avg_tacc.append(np.mean(current_acc))
        
    loss_train.append(loss_avg)
    
    init_tacc_pr.append(avg_init_tacc)
    init_tloss_pr.append(avg_init_tloss)
    
    final_tacc_pr.append(avg_final_tacc)
    final_tloss_pr.append(avg_final_tloss)

    # Evaluate ALL clients (not just participating ones) for this round
    round_all_clients_train_loss = []
    round_all_clients_test_loss = []
    round_all_clients_train_acc = []
    round_all_clients_test_acc = []

    for client_idx in range(args.num_users):
        # Evaluate test performance
        test_loss, test_acc = clients[client_idx].eval_test()
        round_all_clients_test_loss.append(test_loss)
        round_all_clients_test_acc.append(test_acc)

        # Evaluate train performance
        train_loss, train_acc = clients[client_idx].eval_train()
        round_all_clients_train_loss.append(train_loss)
        round_all_clients_train_acc.append(train_acc)

    # Calculate averages across all clients for this round and convert to float
    avg_all_clients_train_loss = float(sum(round_all_clients_train_loss) / len(round_all_clients_train_loss))
    avg_all_clients_test_loss = float(sum(round_all_clients_test_loss) / len(round_all_clients_test_loss))
    avg_all_clients_train_acc = float(sum(round_all_clients_train_acc) / len(round_all_clients_train_acc))
    avg_all_clients_test_acc = float(sum(round_all_clients_test_acc) / len(round_all_clients_test_acc))

    # Store averages for this round (as float numbers, not tensors)
    all_clients_train_loss_per_round.append(avg_all_clients_train_loss)
    all_clients_test_loss_per_round.append(avg_all_clients_test_loss)
    all_clients_train_acc_per_round.append(avg_all_clients_train_acc)
    all_clients_test_acc_per_round.append(avg_all_clients_test_acc)

    ## clear the placeholders for the next round
    if not args.disable_pruning:
        masks.clear()
    w_locals.clear()
    loss_locals.clear()
    init_local_tacc.clear()
    init_local_tloss.clear()
    final_local_tacc.clear()
    final_local_tloss.clear()
    
    ## calling garbage collector
    gc.collect()

    # Update progress bar with current round statistics
    progress_bar.set_postfix({
        'Round': f'{iteration+1}/{args.rounds}',
        'Train_Loss': f'{loss_avg:.3f}',
        'Test_Acc': f'{avg_final_tacc:.3f}',
        'Clients': f'{len(idxs_users)}/{args.num_users}'
    })
    
## Printing Final Test and Train ACC / LOSS
print("\n" + "="*80)
print("[STATS] FINAL RESULTS - ALL ROUNDS SUMMARY (ALL CLIENTS AVERAGE)")
print("="*80)

# Print all rounds' average metrics as lists (ALL clients, not just participating ones)
print(f"\n[UPDATE] All Rounds Train Loss (Average across ALL {args.num_users} clients):")
print(f"   {all_clients_train_loss_per_round}")

print(f"\n[UPDATE] All Rounds Test Loss (Average across ALL {args.num_users} clients):")
print(f"   {all_clients_test_loss_per_round}")

print(f"\n[UPDATE] All Rounds Train Accuracy (Average across ALL {args.num_users} clients):")
print(f"   {all_clients_train_acc_per_round}")

print(f"\n[UPDATE] All Rounds Test Accuracy (Average across ALL {args.num_users} clients):")
print(f"   {all_clients_test_acc_per_round}")

print(f"\n[CHART] SUMMARY STATISTICS:")
print(f"   Total Rounds: {args.rounds}")
print(f"   Total Clients: {args.num_users}")
print(f"   Final Round Train Loss: {all_clients_train_loss_per_round[-1]:.4f}")
print(f"   Final Round Test Loss: {all_clients_test_loss_per_round[-1]:.4f}")
print(f"   Final Round Train Accuracy: {all_clients_train_acc_per_round[-1]:.4f}")
print(f"   Final Round Test Accuracy: {all_clients_test_acc_per_round[-1]:.4f}")

# print(f"\n[LIST] LIST FORMAT RESULTS (ALL CLIENTS AVERAGE):")
# print(f"Train_Loss_All_Rounds = {all_clients_train_loss_per_round}")
# print(f"Test_Loss_All_Rounds = {all_clients_test_loss_per_round}")
# print(f"Train_Acc_All_Rounds = {all_clients_train_acc_per_round}")
# print(f"Test_Acc_All_Rounds = {all_clients_test_acc_per_round}")

print("="*80)

# Print comprehensive communication cost summary
if args.print_communication_summary and communication_logs:
    print(f"\n{'='*80}")
    print(f"[COMM] COMMUNICATION COST SUMMARY")
    print(f"{'='*80}")

    # Calculate total statistics
    total_upload = sum(log['upload_mb'] for log in communication_logs)
    total_download = sum(log['download_mb'] for log in communication_logs)
    total_communication = sum(log['total_mb'] for log in communication_logs)
    avg_per_round = total_communication / len(communication_logs)

    # Get model information
    final_model_info = get_model_size_info(server_state_dict)

    print(f"\n[BUILD]  MODEL INFORMATION:")
    print(f"   Model: {args.model.upper()}")
    print(f"   Dataset: {args.dataset.upper()}")
    print(f"   Total Parameters: {final_model_info['total_params']:,} ({format_parameters(final_model_info['total_params'])})")
    print(f"   Model Size: {final_model_info['size_mb']:.2f} MB")
    print(f"   Pruning: {'Enabled' if not args.disable_pruning else 'Disabled'}")

    print(f"\n[UPDATE] TRAINING CONFIGURATION:")
    print(f"   Total Rounds: {args.rounds}")
    print(f"   Total Clients: {args.num_users}")
    print(f"   Client Participation Rate: {args.frac}")
    avg_participating = sum(log['participating_clients'] for log in communication_logs) / len(communication_logs)
    print(f"   Average Participating Clients: {avg_participating:.1f}")

    print(f"\n[STATS] COMMUNICATION STATISTICS:")
    print(f"   Total Upload: {total_upload:.2f} MB")
    print(f"   Total Download: {total_download:.2f} MB")
    print(f"   Total Communication: {total_communication:.2f} MB")
    print(f"   Total Communication: {total_communication/1024:.3f} GB")
    print(f"   Average per Round: {avg_per_round:.2f} MB")

    print(f"\n[USERS] PER-CLIENT ANALYSIS:")
    avg_per_client_total = total_communication / args.num_users
    print(f"   Average Communication per Client: {avg_per_client_total:.2f} MB")
    print(f"   Communication per Client per Round: {final_model_info['size_mb']*2:.2f} MB")

    print(f"\n[IDEA] EFFICIENCY METRICS:")
    params_per_mb = final_model_info['total_params'] / avg_per_round
    print(f"   Parameters per MB Communication: {params_per_mb:.0f} ({format_parameters(params_per_mb)})")
    print(f"   Communication Efficiency: {1/avg_per_round:.4f} rounds/MB")

    # Print detailed round-by-round breakdown if requested
    if args.print_model_stats:
        print(f"\n[LIST] ROUND-BY-ROUND BREAKDOWN:")
        print(f"{'Round':<8} {'Clients':<8} {'Upload(MB)':<12} {'Download(MB)':<14} {'Total(MB)':<12}")
        print(f"{'-'*8} {'-'*8} {'-'*12} {'-'*14} {'-'*12}")
        for log in communication_logs:
            print(f"{log['round']:<8} {log['participating_clients']:<8} "
                  f"{log['upload_mb']:<12.2f} {log['download_mb']:<14.2f} {log['total_mb']:<12.2f}")

    print(f"{'='*80}")

## Final Global Model Detailed Evaluation
print(f"\n{'='*80}")
print(f"[GLOBAL] FINAL GLOBAL MODEL DETAILED EVALUATION")
print(f"{'='*80}")

# Load the final global model state
net_glob.load_state_dict(server_state_dict)
net_glob.eval()

# Create test data loader for final evaluation
test_loader_final = DataLoader(test_dataset, batch_size=args.local_bs, shuffle=False)

# Collect all predictions and targets for detailed metrics
all_predictions = []
all_targets = []
final_test_loss = 0
final_correct = 0
final_total = 0

with torch.no_grad():
    for data, target in test_loader_final:
        data, target = data.to(args.device), target.to(args.device)
        output = net_glob(data)

        # Calculate loss
        final_test_loss += F.cross_entropy(output, target, reduction='sum').item()

        # Get predictions
        pred = output.argmax(dim=1, keepdim=True)
        final_correct += pred.eq(target.view_as(pred)).sum().item()
        final_total += target.size(0)

        # Store for detailed metrics
        all_predictions.extend(pred.cpu().numpy().flatten())
        all_targets.extend(target.cpu().numpy())

# Calculate final metrics
final_test_loss /= final_total
final_test_accuracy = 100. * final_correct / final_total

print(f"\n[STATS] GLOBAL MODEL FINAL PERFORMANCE:")
print(f"   Test Loss: {final_test_loss:.6f}")
print(f"   Test Accuracy: {final_test_accuracy:.4f}% ({final_correct}/{final_total})")

# Calculate detailed metrics using sklearn
from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, classification_report

# Calculate precision, recall, f1-score
precision, recall, f1, support = precision_recall_fscore_support(
    all_targets, all_predictions, average='weighted', zero_division=0
)

# Calculate per-class metrics
precision_per_class, recall_per_class, f1_per_class, support_per_class = precision_recall_fscore_support(
    all_targets, all_predictions, average=None, zero_division=0
)

print(f"\n[CHART] DETAILED METRICS:")
print(f"   Precision (Weighted): {precision:.4f}")
print(f"   Recall (Weighted): {recall:.4f}")
print(f"   F1-Score (Weighted): {f1:.4f}")

# Calculate False Alarm Rate (for binary classification, use class 0 as normal)
if len(set(all_targets)) == 2:
    tn, fp, fn, tp = confusion_matrix(all_targets, all_predictions).ravel()
    false_alarm_rate = fp / (fp + tn) if (fp + tn) > 0 else 0
    print(f"   False Alarm Rate: {false_alarm_rate:.4f}")

print(f"\n[LIST] PER-CLASS METRICS:")
unique_classes = sorted(set(all_targets))
for i, class_id in enumerate(unique_classes):
    print(f"   Class {class_id}: Precision={precision_per_class[i]:.4f}, "
          f"Recall={recall_per_class[i]:.4f}, F1={f1_per_class[i]:.4f}, Support={support_per_class[i]}")

# Print confusion matrix as 2D list
cm = confusion_matrix(all_targets, all_predictions)
print(f"\n[NUMBERS] CONFUSION MATRIX:")
print(f"   {cm.tolist()}")

# Print all rounds results
print(f"\n[STATS] ALL ROUNDS RESULTS:")
print(f"\n[FIRE] Global Model Test Loss per Round ({len(global_test_loss_per_round)} rounds):")
print(f"   {global_test_loss_per_round}")

print(f"\n[OK] Global Model Test Accuracy per Round ({len(global_test_acc_per_round)} rounds):")
print(f"   {global_test_acc_per_round}")

print(f"\n[TARGET] EXPERIMENT COMPLETED SUCCESSFULLY!")
print(f"   Model: {args.model.upper()} on {args.dataset.upper()}")
print(f"   Training Rounds: {args.rounds}")
print(f"   Total Clients: {args.num_users}")
if communication_logs:
    print(f"   Total Communication: {sum(log['total_mb'] for log in communication_logs):.2f} MB")
print(f"   Final Client Avg Test Accuracy: {all_clients_test_acc_per_round[-1]:.4f}")
print(f"   Final Global Model Test Accuracy: {final_test_accuracy:.4f}%")
print(f"{'='*80}")