#!/usr/bin/env python3
"""
Dataset utilities for centralized training.

This module provides dataset loading functionality extracted from main_u.py
for use in centralized training without triggering the full federated learning pipeline.
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
import torch
from torch.utils.data import Dataset

class CSVDataset(Dataset):
    """Custom Dataset class for CSV data with sliding window and normalization."""

    def __init__(self, csv_path: str, window_size: int = 1, train: bool = True,
                 train_ratio: float = 0.7, scaler=None, random_seed: int = 42,
                 train_indices=None, val_indices=None):
        """
        Initialize CSV dataset with sliding window approach.
        
        Args:
            csv_path: Path to the CSV file
            window_size: Size of the sliding window for temporal features
            train: Whether this is training data (True) or validation data (False)
            train_ratio: Ratio of data to use for training
            scaler: Pre-fitted scaler (for validation set)
            random_seed: Random seed for reproducibility
            train_indices: Pre-computed training indices (for validation set)
            val_indices: Pre-computed validation indices (for validation set)
        """
        self.csv_path = csv_path
        self.window_size = window_size
        self.train = train
        self.train_ratio = train_ratio
        self.random_seed = random_seed
        
        # Load and preprocess data
        self.data, self.targets = self._load_and_preprocess()
        
        # Split data if indices not provided
        if train_indices is None or val_indices is None:
            self.train_indices, self.val_indices = train_test_split(
                range(len(self.data)), 
                train_size=train_ratio, 
                random_state=random_seed,
                stratify=self.targets
            )
        else:
            self.train_indices = train_indices
            self.val_indices = val_indices
        
        # Select appropriate subset
        if self.train:
            self.indices = self.train_indices
        else:
            self.indices = self.val_indices
        
        # Apply indices
        self.data = self.data[self.indices]
        self.targets = self.targets[self.indices]
        
        # Normalize features
        if scaler is None:
            self.scaler = MinMaxScaler()
            self.data = self.scaler.fit_transform(self.data)
        else:
            self.scaler = scaler
            self.data = self.scaler.transform(self.data)
        
        # Convert to tensors
        self.data = torch.FloatTensor(self.data)
        self.targets = torch.LongTensor(self.targets)
        
        print(f"{'Training' if train else 'Validation'} dataset: {len(self.data)} samples")

    def _load_and_preprocess(self):
        """Load and preprocess the CSV data."""
        # Load CSV
        df = pd.read_csv(self.csv_path)

        # 处理可能的无穷大或超出 float64 上限的数值
        # 1) 将正负无穷替换为 NaN
        df.replace([np.inf, -np.inf], np.nan, inplace=True)

        # 2) 将超过 float64 上限 (≈1.79e308) 的极端值裁剪为可表示的最大值
        float64_max: float = np.finfo(np.float64).max
        df = df.applymap(
            lambda x: float64_max if isinstance(x, (int, float, np.floating)) and x > float64_max else
            (-float64_max if isinstance(x, (int, float, np.floating)) and x < -float64_max else x)
        )

        # 3) 对仍存在的 NaN 行进行删除，避免后续 scaler 报错
        if df.isna().any().any():
            before_drop: int = len(df)
            df.dropna(inplace=True)
            print(f"已移除包含 NaN/Inf 的行: {before_drop - len(df)} 条 (剩余 {len(df)})")

        print(f"数据集形状: {df.shape}")

        # 分离特征和标签
        # Assume last column is the label
        features = df.iloc[:, :-1].values.astype(np.float32)
        labels = df.iloc[:, -1].values.astype(np.int64)
        
        # Handle sliding window if window_size > 1
        if self.window_size > 1:
            windowed_features = []
            windowed_labels = []
            
            for i in range(len(features) - self.window_size + 1):
                window = features[i:i + self.window_size]
                windowed_features.append(window.flatten())
                windowed_labels.append(labels[i + self.window_size - 1])
            
            features = np.array(windowed_features)
            labels = np.array(windowed_labels)
        
        return features, labels

    def __len__(self):
        """Return the size of the dataset."""
        return len(self.data)

    def __getitem__(self, idx):
        """Get a single item from the dataset."""
        return self.data[idx], self.targets[idx]

def load_dataset(dataset_name, window_size=1, train_ratio=0.7):
    """
    Load dataset for centralized training.
    
    Args:
        dataset_name: Name of the dataset ('cicids2017', 'carh', 'toniot')
        window_size: Size of the sliding window
        train_ratio: Ratio of data to use for training
    
    Returns:
        tuple: (train_dataset, test_dataset)
    """
    # Dataset paths
    dataset_paths = {
        'cicids2017': 'src/RawData/cicids2017.csv',
        'carh': 'src/RawData/carh.csv',
        'toniot': 'src/RawData/toniot.csv'
    }
    
    if dataset_name not in dataset_paths:
        raise ValueError(f"Unsupported dataset: {dataset_name}. Supported: {list(dataset_paths.keys())}")
    
    csv_path = dataset_paths[dataset_name]
    
    # Create training dataset
    train_dataset = CSVDataset(csv_path, window_size=window_size, train=True, train_ratio=train_ratio)
    
    # Create test dataset using the same scaler and split indices
    test_dataset = CSVDataset(
        csv_path, 
        window_size=window_size, 
        train=False, 
        train_ratio=train_ratio,
        scaler=train_dataset.scaler,
        train_indices=train_dataset.train_indices,
        val_indices=train_dataset.val_indices
    )
    
    return train_dataset, test_dataset

def get_dataset_info(dataset_name):
    """
    Get information about a dataset.
    
    Args:
        dataset_name: Name of the dataset
    
    Returns:
        dict: Dataset information including features, classes, etc.
    """
    dataset_info = {
        'cicids2017': {
            'features': 78,
            'classes': 8,
            'description': 'CICIDS2017 Network Intrusion Detection Dataset'
        },
        'carh': {
            'features': 11,
            'classes': 5,
            'description': 'CarH Automotive Intrusion Detection Dataset'
        },
        'toniot': {
            'features': 42,
            'classes': 10,
            'description': 'ToNIoT IoT Network Intrusion Detection Dataset'
        }
    }
    
    if dataset_name not in dataset_info:
        raise ValueError(f"Unsupported dataset: {dataset_name}")
    
    return dataset_info[dataset_name]
